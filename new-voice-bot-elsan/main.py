import asyncio
import os
import subprocess
import sys
import time
from multiprocessing import Process
from datetime import datetime
import pytz

from pipecat.processors.aggregators.llm_response import LLMUserContextAggregator, LLMAssistantContextAggregator

import aiohttp
import requests
from fastapi import HTT<PERSON><PERSON>xception
from loguru import logger
from supabase import create_client

from fastapi.responses import J<PERSON>NResponse, PlainTextResponse

from pipecat.transports.services.helpers.daily_rest import (
    DailyRESTHelper,
    DailyRoomObject,
    DailyRoomParams,
    DailyRoomProperties,
    DailyRoomSipParams,
)

from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Gather
from twilio.base.exceptions import TwilioRestException


from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

# twilio = Client(
#     os.environ.get("TWILIO_ACCOUNT_SID"), os.environ.get("TWILIO_AUTH_TOKEN")
# )

MAX_SESSION_TIME = 10 * 60  # 10 minutes in seconds

async def send_request(url, data, headers):
    try:
        async with aiohttp.ClientSession() as session:
            await session.post(url, json=data, headers=headers)
        print("Request sent successfully")
    except Exception as e:
        print(f"Error sending request: {str(e)}")

async def delayed_launch(endpoint_url, payload, headers, delay=10):
    await asyncio.sleep(delay)  # Use the delay parameter with default of 5 seconds
    await send_request(endpoint_url, payload, headers)

def handle_twilio_error(e: Exception) -> str:
    """Handle Twilio errors and return appropriate French error message."""
    if isinstance(e, TwilioRestException):
        if e.code == 20003:  # Authentication Error
            return "Une erreur d'authentification est survenue. Veuillez réessayer plus tard."
        elif e.code == 20404:  # Resource not found
            return "La ressource demandée n'a pas été trouvée."
        elif e.code == 21211:  # Invalid phone number
            return "Le numéro de téléphone fourni n'est pas valide."
        elif e.code == 21608:  # Message delivery failed
            return "L'envoi du message a échoué. Veuillez réessayer plus tard."
        else:
            return f"Une erreur est survenue avec le service Twilio (Code: {e.code}). Veuillez réessayer plus tard."
    else:
        return "Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes."

@app.post("/start_bot/{flow_name}/{config_id}")
async def multi_start_bot(request: Request, flow_name: str, config_id: str, response_class=PlainTextResponse):
    try:
        data = {}
        try:
            form_data = await request.form()
            data = dict(form_data)
        except Exception as e:
            logger.error(f"Error parsing form data: {e}")
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Une erreur est survenue lors du traitement de votre appel. Veuillez réessayer plus tard.", rate="1.15")
            return PlainTextResponse(content=str(resp), media_type="application/xml")
    
        logger.info(f"Received call data: {data}")
        
        callId = data.get('CallSid')
        caller_phone = data.get('From')

        france_tz = pytz.timezone('Europe/Paris')
        current_time = datetime.now(france_tz)
        current_hour = current_time.hour

        # Check for anonymous call
        if caller_phone in ['anonymous', '+anonymous', '+33974368037', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        room = await create_room()
        logger.info(f"Created room: {room}")

        # Make request to Cerebrium endpoint
        endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/new-voice-bot-elsan/launch"
        payload = {
            "room_url": room["room_url"],
            "token": room["token"],
            "sipUri": room["sip_endpoint"],
            "callId": callId,
            "flow_name": flow_name,
            "config_id": config_id,  # Use the config_id from the URL parameter
            "caller_phone": caller_phone
        }
        headers = {
            "Content-Type": "application/json",
        }

        if config_id in ['config32', 'config73']:
            # Start the delayed launch task with 25 seconds delay
            asyncio.create_task(delayed_launch(endpoint_url, payload, headers, delay=22))
        elif config_id in ['config58', 'config59', "config60"]:
            asyncio.create_task(delayed_launch(endpoint_url, payload, headers, delay=11))
        else:
            # Default delay for other configs
            asyncio.create_task(delayed_launch(endpoint_url, payload, headers))
            

        resp = VoiceResponse()
        logger.debug("Creating TwiML response with intro music")
        try:
            if config_id == 'config32':
                resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/revelis-ourcq-2.mp3")
            elif config_id == 'config73':
                resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/revelis-pyramides-2.mp3")
            elif config_id in ['config58', 'config59', "config60"]:
                # Create a Gather to listen for any key press
                gather = Gather(
                    action=f"https://api.cortex.cerebrium.ai/v4/p-7f7215fa/new-voice-bot-elsan/rgpd_sound",  # Action URL when key is pressed
                    num_digits=1,  # Listen for 1 digit
                    timeout=30,  # Wait 30 seconds for input
                    method="POST"
                )
                # Play the initial sound while listening for key press
                gather.play(url="https://vocca.s3.amazonaws.com/audio/elsan3-20250620-161647.mp3")
                resp.append(gather)

            
            twiml_response = str(resp)
            logger.debug(f"Generated TwiML: {twiml_response}")
            return PlainTextResponse(
                content=twiml_response,
                
                media_type="application/xml"
            )
        except Exception as e:
            logger.error(f"Error generating TwiML: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    except TwilioRestException as e:
        logger.error(f"Twilio error: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message=handle_twilio_error(e), rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")
    except Exception as e:
        logger.error(f"Unexpected error in start_bot: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message="Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes.", rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")


async def create_room():
    params = DailyRoomParams(
        properties=DailyRoomProperties(
            exp=time.time() + MAX_SESSION_TIME,  # Add expiry time
            sip=DailyRoomSipParams(
                display_name="sip-dialin",
                video=False,
                sip_mode="dial-in",
                num_endpoints=1,
            )
        )
    )

    # Create sip-enabled Daily room via REST
    try:
        async with aiohttp.ClientSession() as session:
            daily_helper = DailyRESTHelper(
                daily_api_key=os.environ.get("DAILY_API_KEY"),
                daily_api_url="https://api.daily.co/v1",
                aiohttp_session=session
            )
            room: DailyRoomObject = await daily_helper.create_room(params=params)
            token = await daily_helper.get_token(room.url, MAX_SESSION_TIME)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unable to provision room {e}")

    print(f"Daily room returned {room.url} {room.config.sip_endpoint}")

    return {
        "room_url": room.url,
        "sip_endpoint": room.config.sip_endpoint,
        "token": token,
    }


def create_token(room_name: str):
    url = "https://api.daily.co/v1/meeting-tokens"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.environ.get('DAILY_TOKEN')}",
    }
    data = {"properties": {"room_name": room_name}}

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        token_info = response.json()
        return token_info
    else:
        logger.error(f"Failed to create token: {response.status_code}")
        return None


@app.post("/launch")
async def launch(request: Request):
    try:
        # Parse the JSON body
        data = await request.json()
        
        # Extract parameters from the request body
        room_url = data["room_url"]
        token = data["token"]
        sipUri = data["sipUri"]
        callId = data["callId"]
        flow_name = data["flow_name"]
        config_id = data["config_id"]
        caller_phone = data["caller_phone"]

        # Get the directory of the current script
        current_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(current_dir, f"{flow_name}.py")

        # Run the klarity_flow script with subprocess
        process = subprocess.Popen([
            sys.executable,
            script_path,  # Use the full path to the script
            "-u", room_url,
            "-t", token,
            "-i", callId,
            "-s", sipUri,
            "-c", config_id,
            "-p", caller_phone
        ])
        
        return {"status": "success", "message": "Klarity flow started"}
        
    except FileNotFoundError as e:
        logger.error(f"Script not found: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Script not found: {str(e)}")
    except KeyError as e:
        logger.error(f"Missing required parameter: {str(e)}")
        raise HTTPException(status_code=422, detail=f"Missing required parameter: {str(e)}")
    except Exception as e:
        logger.error(f"Error running klarity flow: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error running klarity flow: {str(e)}")


@app.post("/rgpd_sound")
async def play_rgpd_sound():
    """Simple endpoint to play RGPD sound when any key is pressed"""
    logger.info("Playing RGPD sound")

    resp = VoiceResponse()
    resp.play(url="https://vocca.s3.amazonaws.com/audio/elsan_rgpd-20250624-135337.mp3")
    return PlainTextResponse(content=str(resp), media_type="application/xml")
