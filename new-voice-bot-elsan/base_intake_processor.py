import os
from datetime import datetime
import asyncio
from loguru import logger
from twilio.twiml.voice_response import VoiceResponse
from twilio.rest import Client as TwilioClient
from pipecat.services.openai import OpenAILLMContextFrame
from pipecat.processors.frame_processor import FrameDirection
from pipecat.frames.frames import EndFrame

class BaseIntakeProcessor:
    def __init__(self, context, llm, tts, callId, config, patient_data):
        self._context = context
        self._llm = llm
        self._tts = tts
        self._callId = callId
        self._config = config
        self._new_patient = None
        self._request_data = {}
        self._patient_data = patient_data
        self._appt_data = {}
        self._caller_phone_number = None
        self._api_token = os.getenv("SECRET_DOCTOLIB")
        self._call_start_time = datetime.now()
        self._call_forwarded = False
        self._twilio_client = TwilioClient(os.getenv("TWILIO_ACCOUNT_SID"), os.getenv("TWILIO_AUTH_TOKEN"))
        self._api_call_in_progress = False

        self._fetch_caller_phone_number()
        self._initialize_context()
        self.day_of_the_week = {
            0: "Lundi",
            1: "Mardi",
            2: "Mercredi",
            3: "Jeudi",
            4: "Vendredi",
            5: "Samedi",
            6: "Dimanche"
        }
        

    def _initialize_context(self):
        # Common initialization logic
        pass

    def _fetch_caller_phone_number(self):
        try:
            call = self._twilio_client.calls(self._callId).fetch()
            self._caller_phone_number = call.from_formatted
        except Exception as e:
            logger.error(f"Failed to fetch caller's phone number: {str(e)}")

    async def forward_call(self, function_name, tool_call_id, args, llm, context, result_callback, forward_number="+***********"):
        if self._call_forwarded:
            logger.info("Call already forwarded, ignoring repeated forward attempts.")
            return

        try:
            response = VoiceResponse()
            response.play('https://vocca-daily.s3.us-east-1.amazonaws.com/transfert-audio-2.wav')
            response.dial(forward_number)
            twiml_response = str(response)
            updated_call = await asyncio.to_thread(self._twilio_client.calls(self._callId).update, twiml=twiml_response)
            self._call_forwarded = True
            logger.info(f"Call forwarded to {forward_number}. Updated call status: {updated_call.status}")
            
            return
        except Exception as e:
            logger.error(f"Failed to play audio or forward call: {str(e)}")
            # Add EndFrame even if there's an exception
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)

    async def end_call(self, function_name, tool_call_id, args, llm, context, result_callback):
        # Say goodbye using TTS directly
        await self._tts.say("Je vous remercie pour votre appel. Je vous souhaite une excellente journée !")

        # Small delay to ensure TTS completes
        await asyncio.sleep(6)

        try:
            # End the Twilio call
            updated_call = await asyncio.to_thread(
                self._twilio_client.calls(self._callId).update,
                status="completed"
            )
            logger.info(f"Call ended successfully. Final call status: {updated_call.status}")
            
            # Push EndFrame to trigger participant_left
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)
            
        except Exception as e:
            logger.error(f"Error ending call: {str(e)}")
            # Still try to push EndFrame even if Twilio update fails
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)

    async def appt_intent(self, function_name, tool_call_id, args, llm, context, result_callback):
        intent = args["intent"]
        
        # Check if there are multiple future appointments and intent is to modify
        if intent == "modifier" and self._patient_data.get("future_appointments_count", 0) > 1:
            # Go to task mode instead of trying to handle modification directly
            await self.set_question(function_name, tool_call_id, {"new_patient": False}, llm, context, result_callback)
            return
        
        # Default behavior for other cases
        if intent == "nouveau":
            await self.appt_type(function_name, tool_call_id, args, llm, context, result_callback)
        elif intent == "modifier":
            await self.appt_confirm(function_name, tool_call_id, args, llm, context, result_callback, "modifier")
        elif intent == "annuler":
            await self.appt_confirm(function_name, tool_call_id, args, llm, context, result_callback, "annuler")

    async def search_patient(self, function_name, tool_call_id, args, llm, context, result_callback):
        # Implementation of search_patient
        pass

    async def set_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        # Implementation of set_appt
        pass

    async def appt_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        # Implementation of appt_type
        pass

    async def appt_check_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def ask_date(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def check_availability(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def confirm_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def save_new_patient_info(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def confirm_new_patient_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def cancel_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def recap_question(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def set_question(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def child_check(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def general_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def urgent(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def route_center(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def retard(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def no_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def appt_confirm(self, function_name, tool_call_id, args, llm, context, result_callback, intent):
        pass

    async def appt_double_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def appt_unique_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def multiple_availability(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def appt_check_double_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def get_birth_date(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass

    async def get_last_name(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass
    
    async def appt_double_type_1(self, function_name, tool_call_id, args, llm, context, result_callback):
        pass
