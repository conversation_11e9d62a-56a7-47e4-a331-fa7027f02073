import asyncio
import os
import subprocess
import sys
import time
from multiprocessing import Process
from datetime import datetime
import pytz

from pipecat.processors.aggregators.llm_response import LLMUserContextAggregator, LLMAssistantContextAggregator

import aiohttp
import requests
from fastapi import HTT<PERSON><PERSON>xception
from loguru import logger
from supabase import create_client

from fastapi.responses import J<PERSON>NResponse, PlainTextResponse

from pipecat.transports.services.helpers.daily_rest import (
    DailyRESTHelper,
    DailyRoomObject,
    DailyRoomParams,
    DailyRoomProperties,
    DailyRoomSipParams,
)

from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Gather
from twilio.base.exceptions import TwilioRestException

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

# twilio = Client(
#     os.environ.get("TWILIO_ACCOUNT_SID"), os.environ.get("TWILIO_AUTH_TOKEN")
# )

MAX_SESSION_TIME = 10 * 60  # 10 minutes in seconds

async def send_request(url, data, headers):
    try:
        async with aiohttp.ClientSession() as session:
            await session.post(url, json=data, headers=headers)
        print("Request sent successfully")
    except Exception as e:
        print(f"Error sending request: {str(e)}")

async def delayed_launch(endpoint_url, payload, headers):
    await asyncio.sleep(5)  # Wait for 10 seconds
    await send_request(endpoint_url, payload, headers)

def handle_twilio_error(e: Exception) -> str:
    """Handle Twilio errors and return appropriate French error message."""
    if isinstance(e, TwilioRestException):
        if e.code == 20003:  # Authentication Error
            return "Une erreur d'authentification est survenue. Veuillez réessayer plus tard."
        elif e.code == 20404:  # Resource not found
            return "La ressource demandée n'a pas été trouvée."
        elif e.code == 21211:  # Invalid phone numbesr
            return "Le numéro de téléphone fourni n'est pas valide."
        elif e.code == 21608:  # Message delivery failed
            return "L'envoi du message a échoué. Veuillez réessayer plus tard."
        else:
            return f"Une erreur est survenue avec le service Twilio (Code: {e.code}). Veuillez réessayer plus tard."
    else:
        return "Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes."

@app.post("/start_bot/{flow_name}/{config_id}")
async def multi_start_bot(request: Request, flow_name: str, config_id: str, response_class=PlainTextResponse):
    try:
        data = {}
        try:
            form_data = await request.form()
            data = dict(form_data)
        except Exception as e:
            logger.error(f"Error parsing form data: {e}")
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Une erreur est survenue lors du traitement de votre appel. Veuillez réessayer plus tard.", rate="1.15")
            return PlainTextResponse(content=str(resp), media_type="application/xml")
    
        logger.info(f"Received call data: {data}")
        callId = data.get('CallSid')
        caller_phone = data.get('From')

        france_tz = pytz.timezone('Europe/Paris')
        current_time = datetime.now(france_tz)
        current_hour = current_time.hour

        # Time-based routing for config25
        if config_id == 'config25':
            # Get current time in France
            
            # If time is between 8:30 AM and 7:00 PM Monday through Saturday (0-5)
            if current_time.weekday() < 6 and (8 <= current_hour < 19 or (current_hour == 8 and current_time.minute >= 30)):
                # Forward to the specified number during business hours
                resp = VoiceResponse()
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )
            
        if config_id == 'config40':
            # If time is between 9:30 AM and 6:30 PM Monday through Friday (0-4)
            if (current_time.weekday() < 5 and 
                ((9 < current_hour < 18) or 
                 (current_hour == 9 and current_time.minute >= 30) or
                 (current_hour == 18 and current_time.minute <= 30))):
                # Forward to the specified number during business hours
                resp = VoiceResponse()
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )

        if config_id == 'config2':
            # New business hours logic:
            # Lundi, Mardi, Jeudi: 9h-12h et 14h-18h
            # Mercredi: 9h-12h et 14h-16h00
            # Vendredi: 9h-12h et 14h-16h30
            # Weekend: fermé
            # Entre midi et 14h: fermé
            
            is_outside_hours = False
            weekday = current_time.weekday()  # 0=Monday, 1=Tuesday, etc.
            
            # Weekend (Saturday or Sunday)
            if weekday >= 5:
                is_outside_hours = True
            else:
                # Define business hours based on day
                if weekday in [0, 1, 3]:  # Monday, Tuesday, Thursday
                    # 9h-12h and 14h-18h
                    if (current_hour < 9 or 
                        (current_hour >= 12 and current_hour < 14) or 
                        current_hour >= 18):
                        is_outside_hours = True
                elif weekday == 2:  # Wednesday
                    # 9h-12h and 14h-16h00
                    if (current_hour < 9 or 
                        (current_hour >= 12 and current_hour < 16) or 
                        current_hour >= 16):
                        is_outside_hours = True
                elif weekday == 4:  # Friday
                    # 9h-12h and 14h-16h30
                    if (current_hour < 9 or 
                        (current_hour >= 12 and current_hour < 14) or 
                        current_hour > 16 or 
                        (current_hour == 16 and current_time.minute > 30)):
                        is_outside_hours = True
            
            if is_outside_hours:
                # Play specific sound outside business hours
                resp = VoiceResponse()
                resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/repondeur_agyl.mp3")
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )

        # Check for anonymous call
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33', '+***********', '+***********', '+***********']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        if not callId:
            logger.error("Missing CallSid in request")
            raise HTTPException(status_code=400, detail="Missing 'CallSid' in request")

        room = await create_room()
        logger.info(f"Created room: {room}")

        # Make request to Cerebrium endpoint
        endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/launch"
        payload = {
            "room_url": room["room_url"],
            "token": room["token"],
            "sipUri": room["sip_endpoint"],
            "callId": callId,
            "flow_name": flow_name,
            "config_id": config_id,  # Use the config_id from the URL parameter
            "caller_phone": caller_phone
        }
        headers = {
            "Content-Type": "application/json",
        }

        # Start the delayed launch task
        asyncio.create_task(delayed_launch(endpoint_url, payload, headers))

        resp = VoiceResponse()
        logger.debug("Creating TwiML response with intro music")
        try:
            resp.play(
                url="https://vocca-daily.s3.us-east-1.amazonaws.com/new-loading.mp3", 
                loop=10
            )

            twiml_response = str(resp)
            logger.debug(f"Generated TwiML: {twiml_response}")
            return PlainTextResponse(
                content=twiml_response,
                media_type="application/xml"
            )
        except Exception as e:
            logger.error(f"Error generating TwiML: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    except TwilioRestException as e:
        logger.error(f"Twilio error: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message=handle_twilio_error(e), rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")
    except Exception as e:
        logger.error(f"Unexpected error in start_bot: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message="Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes.", rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")


async def create_room():
    params = DailyRoomParams(
        properties=DailyRoomProperties(
            exp=time.time() + MAX_SESSION_TIME,  # Add expiry time
            sip=DailyRoomSipParams(
                display_name="sip-dialin",
                video=False,
                sip_mode="dial-in",
                num_endpoints=1,
            )
        )
    )

    # Create sip-enabled Daily room via REST
    try:
        async with aiohttp.ClientSession() as session:
            daily_helper = DailyRESTHelper(
                daily_api_key=os.environ.get("DAILY_API_KEY"),
                daily_api_url="https://api.daily.co/v1",
                aiohttp_session=session
            )
            room: DailyRoomObject = await daily_helper.create_room(params=params)
            token = await daily_helper.get_token(room.url, MAX_SESSION_TIME)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unable to provision room {e}")

    print(f"Daily room returned {room.url} {room.config.sip_endpoint}")

    return {
        "room_url": room.url,
        "sip_endpoint": room.config.sip_endpoint,
        "token": token,
    }


def create_token(room_name: str):
    url = "https://api.daily.co/v1/meeting-tokens"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.environ.get('DAILY_TOKEN')}",
    }
    data = {"properties": {"room_name": room_name}}

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        token_info = response.json()
        return token_info
    else:
        logger.error(f"Failed to create token: {response.status_code}")
        return None


@app.post("/launch")
async def launch(request: Request):
    try:
        # Parse the JSON body
        data = await request.json()
        
        # Extract parameters from the request body
        room_url = data["room_url"]
        token = data["token"]
        sipUri = data["sipUri"]
        callId = data["callId"]
        flow_name = data["flow_name"]
        config_id = data["config_id"]
        caller_phone = data["caller_phone"]

        # Get the directory of the current script
        current_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(current_dir, f"{flow_name}.py")

        # Run the klarity_flow script with subprocess
        process = subprocess.Popen([
            sys.executable,
            script_path,  # Use the full path to the script
            "-u", room_url,
            "-t", token,
            "-i", callId,
            "-s", sipUri,
            "-c", config_id,
            "-p", caller_phone
        ])
        
        return {"status": "success", "message": "Klarity flow started"}
        
    except FileNotFoundError as e:
        logger.error(f"Script not found: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Script not found: {str(e)}")
    except KeyError as e:
        logger.error(f"Missing required parameter: {str(e)}")
        raise HTTPException(status_code=422, detail=f"Missing required parameter: {str(e)}")
    except Exception as e:
        logger.error(f"Error running klarity flow: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error running klarity flow: {str(e)}")

    
    
@app.post("/optimaz_start_bot", response_class=PlainTextResponse)
async def optimaz_start_bot(request: Request):
    try:
        # Get initial form data
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        # Get the digits from the form data
        digits = data.get('Digits')
        
        if not digits:
            # If no digits, present the menu
            resp = VoiceResponse()
            gather = Gather(numDigits=1, action="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/optimaz_start_bot")
            gather.say(language="fr-FR",
                      message="Bonjour, bienvenue au centre d'imagerie Optimaze de Nice. Pour les I R M et les Scanners, appuyez sur 1. Pour tout autre sujet, appuyez sur 2.",
                      rate="1.15")
            resp.append(gather)
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/optimaz_start_bot")
            return str(resp)
        
        # Process the digits if present
        if digits == '1':
            # Forward to reception
            resp = VoiceResponse()
            resp.dial('+33423320066')  # Replace with actual reception phone number
            return str(resp)
        elif digits == '2':
            # Launch the bot
            room = await create_room()
            
            # Call launch endpoint
            endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/launch"
            payload = {
                "room_url": room["room_url"],
                "token": room["token"],
                "sipUri": room["sip_endpoint"],
                "callId": data.get('CallSid'),
                "flow_name": "opticim_flow",
                "config_id": "config21",
                "caller_phone": caller_phone
            }
            headers = {
                "Content-Type": "application/json",
            }

            # Start the delayed launch task
            asyncio.create_task(delayed_launch(endpoint_url, payload, headers))

            resp = VoiceResponse()
            resp.play(
                url="https://vocca-daily.s3.us-east-1.amazonaws.com/new-loading.mp3", 
                loop=10
            )
            # Add minimum wait time
            resp.pause(length=10)

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error in optimaz_start_bot: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
@app.post("/primary_flow", response_class=PlainTextResponse)
async def primary_start_bot(request: Request):
    try:
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        callId = data.get('CallSid')
        if not callId:
            raise HTTPException(status_code=500, detail="Missing 'CallSid' in request")
        
        content = "Voici nos liens utiles pour prendre rendez-vous ou discuter avec le cabinet : https://linktr.ee/primarycauderan"
        
        webhook_url = "https://n8n-self-hosted-vocca.onrender.com/webhook/46ace88a-b96e-4900-8dc3-4e5210f69d53"
        payload = {
            "to": "" + caller_phone,
            "content": content,
            "from": "Primary"
        }
        
        # Create task but don't await it
        asyncio.create_task(asyncio.to_thread(requests.post, webhook_url, json=payload))

        # First, create the TwiML response to play the sound
        resp = VoiceResponse()
        resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/primary.wav")
        
        # After sound plays (24 seconds), create room and launch bot
        async def delayed_launch():
            await asyncio.sleep(24)  # Wait for sound to complete
            room = await create_room()
            
            # Call launch endpoint
            endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/launch"
            payload = {
                "room_url": room["room_url"],
                "token": room["token"],
                "sipUri": room["sip_endpoint"],
                "callId": callId,
                "flow_name": "primary_flow",
                "config_id": "config52",
                "caller_phone": caller_phone
            }
            headers = {
                "Content-Type": "application/json",
            }
            await send_request(endpoint_url, payload, headers)

        # Start the delayed launch task
        asyncio.create_task(delayed_launch())
        
        # Add silent pause after sound
        resp.pause(length=80)

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error in primary_start_bot: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/wait", response_class=PlainTextResponse)
async def wait():
    try:
        resp = VoiceResponse()
        resp.play(
            url="https://vocca-daily.s3.amazonaws.com/all-wait.mp3", 
            loop=10
        )
        # Add minimum wait time
        resp.pause(length=10)
        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
    except Exception as e:
        logger.error(f"Error in wait endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
@app.post("/after_recall", response_class=PlainTextResponse)
async def after_recall(request: Request):
    try:
        # Get form data
        data = await request.form()
        caller_phone = data.get('From')
        logger.info(f"Received after_recall request for phone number: {caller_phone}")
        
        # Initialize Supabase client with service role key
        supabase_url = os.environ.get("SUPABASE_URL")
        service_role_key = os.environ.get("SERVICE_ROLE_KEY")
        supabase = create_client(supabase_url, service_role_key)
        logger.info("Supabase client initialized")
                
        # Try querying the tables
        try:
            logger.info("Querying recalls table for recent active recalls")
            recalls_check = supabase.table("recalls") \
                .select("*") \
                .eq("phone_number", caller_phone) \
                .order('created_at', desc=True) \
                .limit(3) \
                .execute()
            
            logger.info(f"Found {len(recalls_check.data)} recent recalls for this phone number")
            
            if recalls_check.data:
                for recall in recalls_check.data:
                    logger.info(f"Processing recall: {recall}")
                    
                    # Update the recall status
                    logger.info(f"Updating recall with ID: {recall.get('id')}")
                    recalls_update = supabase.table("recalls") \
                        .update({"status": True, "confirmed": True}) \
                        .eq("callId", recall.get('callId')) \
                        .execute()
                    logger.info(f"Recall update response: {recalls_update.data}")
                    
                    # Get appointment data for Doctolib update
                    config = recall.get("config")
                    appt_id = recall.get("appt_id")
                    logger.info(f"Retrieved config: {config} and appt_id: {appt_id}")
                    
                    if config and appt_id:
                        # Make PUT request to Doctolib API
                        doctolib_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/modify-appt"
                        doctolib_payload = {
                            "config": config,
                            "id": appt_id,
                            "note": "Rendez-vous confirmé par Vocca suite à un rappel du patient"
                        }
                        doctolib_headers = {
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {os.environ.get('SECRET_DOCTOLIB')}"
                        }
                        
                        try:
                            logger.info(f"Making Doctolib API request with payload: {doctolib_payload}")
                            doctolib_response = requests.put(doctolib_url, json=doctolib_payload, headers=doctolib_headers)
                            logger.info(f"Doctolib API response: {doctolib_response.status_code} - {doctolib_response.text}")
                            
                            # Additional API call based on configs
                            confirm_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/confirm-appt"
                            
                            # Handle specific configs
                            if config in ["config57", "config90", "config80", "config79", "config78", "config81", "config84", 
                                         "config85", "config90", "config91", "config86", "config82", "config83", "config87", 
                                         "config88", "config89"]:
                                logger.info(f"Making additional confirm-appt API call for {config} with c5b5d field")
                                confirm_payload = {
                                    "config": config,
                                    "appt_id": appt_id,
                                    "custom_fields": {
                                        "c5b5d": "Oui"
                                    }
                                }
                                confirm_response = requests.put(confirm_url, json=confirm_payload, headers=doctolib_headers)
                                logger.info(f"Custom field confirm API response: {confirm_response.status_code} - {confirm_response.text}")
                            elif config in ["config69", "config70"]:
                                logger.info(f"Making additional confirm-appt API call for {config}")
                                confirm_payload = {
                                    "config": config,
                                    "appt_id": appt_id,
                                    "custom_fields": {
                                        "2d190": "OUI"
                                    }
                                }
                                confirm_response = requests.put(confirm_url, json=confirm_payload, headers=doctolib_headers)
                                logger.info(f"Confirm API response: {confirm_response.status_code} - {confirm_response.text}")
                            elif config == "config53":
                                logger.info(f"Making additional confirm-appt API call for config53")
                                confirm_payload = {
                                    "config": config,
                                    "appt_id": appt_id,
                                    "custom_fields": {
                                        "eefff": "[\"Confirmé\"]"
                                    }
                                }
                                confirm_response = requests.put(confirm_url, json=confirm_payload, headers=doctolib_headers)
                                logger.info(f"Config53 confirm API response: {confirm_response.status_code} - {confirm_response.text}")
                            elif config == "config119":
                                logger.info(f"Making additional confirm-appt API call for config119")
                                confirm_payload = {
                                    "config": config,
                                    "appt_id": appt_id,
                                    "custom_fields": {
                                        "97425": "Confirmé par Vocca"
                                    }
                                }
                                confirm_response = requests.put(confirm_url, json=confirm_payload, headers=doctolib_headers)
                                logger.info(f"Config119 confirm API response: {confirm_response.status_code} - {confirm_response.text}")
                        except Exception as e:
                            logger.error(f"Error making Doctolib API request: {str(e)}", exc_info=True)
            else:
                logger.warning(f"No active recalls found for phone number: {caller_phone}")
        except Exception as e:
            logger.error(f"Error with recalls table: {str(e)}", exc_info=True)
            logger.error(f"Full recalls_check data: {recalls_check.data if 'recalls_check' in locals() else 'Not available'}")

        # Create TwiML response
        resp = VoiceResponse()
        resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/after_recall_2.mp3")
        # Add minimum wait time
        resp.pause(length=10)

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
    except Exception as e:
        logger.error(f"Error in after_recall endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/after_recall_manin_crimee", response_class=PlainTextResponse)
async def after_recall_manin_crimee(request: Request):
    try:
        # Get form data
        data = await request.form()
        caller_phone = data.get('From')
        logger.info(f"Received after_recall_manin_crimee request for phone number: {caller_phone}")
        
        # Get the digits from the form data
        digits = data.get('Digits')
        
        if not digits:
            # If no digits, present the menu
            resp = VoiceResponse()
            gather = Gather(numDigits=1, action="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/after_recall_manin_crimee")
            gather.say(language="fr-FR",
                       message="Merci de nous avoir rappelés, tapez 1 pour confirmer votre rendez-vous, tapez 2 pour annuler, modifier ou pour toute question sur votre rendez-vous.",
                       rate="1.15")
            resp.append(gather)
            
            # If the user doesn't input anything, redirect to the same endpoint 
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/after_recall_manin_crimee")
            
            return PlainTextResponse(
                content=str(resp),
                media_type="application/xml"
            )
        
        # Process the digits if present
        if digits == '1':
            # Option 1: Confirm appointment - same as after_recall
            # Initialize Supabase client with service role key
            supabase_url = os.environ.get("SUPABASE_URL")
            service_role_key = os.environ.get("SERVICE_ROLE_KEY")
            supabase = create_client(supabase_url, service_role_key)
            logger.info("Supabase client initialized")
                    
            # Try querying the tables
            try:
                logger.info("Querying recalls table for recent active recalls")
                recalls_check = supabase.table("recalls") \
                    .select("*") \
                    .eq("phone_number", caller_phone) \
                    .order('created_at', desc=True) \
                    .limit(3) \
                    .execute()
                
                logger.info(f"Found {len(recalls_check.data)} recent recalls for this phone number")
                
                if recalls_check.data:
                    for recall in recalls_check.data:
                        logger.info(f"Processing recall: {recall}")
                        
                        # Update the recall status
                        logger.info(f"Updating recall with ID: {recall.get('id')}")
                        recalls_update = supabase.table("recalls") \
                            .update({"status": True, "confirmed": True}) \
                            .eq("callId", recall.get('callId')) \
                            .execute()
                        logger.info(f"Recall update response: {recalls_update.data}")
                        
                        # Get appointment data for Doctolib update
                        config = recall.get("config")
                        appt_id = recall.get("appt_id")
                        logger.info(f"Retrieved config: {config} and appt_id: {appt_id}")
                        
                        if config and appt_id:
                            # Make PUT request to Doctolib API
                            doctolib_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/modify-appt"
                            doctolib_payload = {
                                "config": config,
                                "id": appt_id,
                                "note": "Rendez-vous confirmé par Vocca suite à un rappel du patient"
                            }
                            doctolib_headers = {
                                "Content-Type": "application/json",
                                "Authorization": f"Bearer {os.environ.get('SECRET_DOCTOLIB')}"
                            }
                            
                            try:
                                logger.info(f"Making Doctolib API request with payload: {doctolib_payload}")
                                doctolib_response = requests.put(doctolib_url, json=doctolib_payload, headers=doctolib_headers)
                                logger.info(f"Doctolib API response: {doctolib_response.status_code} - {doctolib_response.text}")
                                
                                # Additional API call for config69 or config70
                                if config in ["config69", "config70"]:
                                    logger.info(f"Making additional confirm-appt API call for {config}")
                                    confirm_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/confirm-appt"
                                    confirm_payload = {
                                        "config": config,
                                        "appt_id": appt_id,
                                        "custom_fields": {
                                            "2d190": "OUI"
                                        }
                                    }
                                    confirm_response = requests.put(confirm_url, json=confirm_payload, headers=doctolib_headers)
                                    logger.info(f"Confirm API response: {confirm_response.status_code} - {confirm_response.text}")
                            except Exception as e:
                                logger.error(f"Error making Doctolib API request: {str(e)}", exc_info=True)
                else:
                    logger.warning(f"No active recalls found for phone number: {caller_phone}")
            except Exception as e:
                logger.error(f"Error with recalls table: {str(e)}", exc_info=True)
                logger.error(f"Full recalls_check data: {recalls_check.data if 'recalls_check' in locals() else 'Not available'}")

            # Create TwiML response for confirmation
            resp = VoiceResponse()
            resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/after_recall_2.mp3")
            # Add minimum wait time
            resp.pause(length=10)

        elif digits == '2':
            # Option 2: Transfer call to 01 44 52 01 00
            resp = VoiceResponse()
            resp.dial('+***********')
        else:
            # Invalid option
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Option non valide. Veuillez réessayer.", rate="1.15")
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/after_recall_manin_crimee")

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
    except Exception as e:
        logger.error(f"Error in after_recall_manin_crimee endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/opticim_deltour", response_class=PlainTextResponse)
async def opticim_deltour(request: Request):
    try:
        # Get initial form data
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        # Get the digits from the form data
        digits = data.get('Digits')
        
        if not digits:
            # If no digits, present the menu
            resp = VoiceResponse()
            gather = Gather(numDigits=1, action="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/opticim_deltour")
            gather.say(language="fr-FR",
                      message="Bonjour, le cabinet de Deltour est définitivement fermé. Pour toute demande de radio, échographie ou mammographie, nous pouvons vous transférer. Tapez 1 pour le cabinet Opticim de Fonvieille. Tapez 2 pour le cabinet Opticim de La Marqueille.",
                      rate="1.15")
            resp.append(gather)
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/opticim_deltour")
            return str(resp)
        
        # Process the digits if present
        if digits == '1':
            # Forward to Fonvieille using Twilio's Call Transfer capability
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Nous vous transférons vers le cabinet Opticim de Fonvieille. Veuillez patienter.", rate="1.15")
            # Create a URL that Twilio will call to connect with destination number
            transfer_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/transfer?to=%2B33561213725"
            resp.redirect(transfer_url)
            return str(resp)
        elif digits == '2':
            # Forward to La Marqueille using Twilio's Call Transfer capability
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Nous vous transférons vers le cabinet Opticim de La Marqueille. Veuillez patienter.", rate="1.15")
            # Create a URL that Twilio will call to connect with destination number
            transfer_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/transfer?to=%2B33561249830"
            resp.redirect(transfer_url)
            return str(resp)
        else:
            # Invalid option
            resp = VoiceResponse()
            resp.say(language="fr-FR", 
                    message="Option non valide. Veuillez réessayer.", 
                    rate="1.15")
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/opticim_deltour")
            return str(resp)

    except Exception as e:
        logger.error(f"Error in opticim_deltour: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/transfer", response_class=PlainTextResponse)
async def transfer(request: Request):
    try:
        # Get form data and query parameters
        form_data = await request.form()
        query_params = request.query_params
        
        # Get the destination phone number from query params
        to_number = query_params.get('to')
        
        if not to_number:
            logger.error("Missing 'to' parameter in transfer request")
            raise HTTPException(status_code=400, detail="Missing 'to' parameter")
        
        # Create TwiML to dial the destination number
        resp = VoiceResponse()
        resp.dial(to_number, timeout=30, record='record-from-answer', 
                  recording_status_callback="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot/recording_status")
        
        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
        
    except Exception as e:
        logger.error(f"Error in transfer endpoint: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", 
                message="Nous ne pouvons pas vous transférer pour le moment. Veuillez réessayer plus tard.", 
                rate="1.15")
        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )