import asyncio
import os
import subprocess
import sys
import time
from multiprocessing import Process
from datetime import datetime
import pytz

from pipecat.processors.aggregators.llm_response import LLMUserContextAggregator, LLMAssistantContextAggregator

import aiohttp
import requests
from fastapi import HTT<PERSON><PERSON>xception
from loguru import logger
from supabase import create_client

from fastapi.responses import J<PERSON>NResponse, PlainTextResponse

from pipecat.transports.services.helpers.daily_rest import (
    DailyRESTHelper,
    DailyRoomObject,
    DailyRoomParams,
    DailyRoomProperties,
    DailyRoomSipParams,
)

from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Gather
from twilio.base.exceptions import TwilioRestException


from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

# twilio = Client(
#     os.environ.get("TWILIO_ACCOUNT_SID"), os.environ.get("TWILIO_AUTH_TOKEN")
# )

MAX_SESSION_TIME = 10 * 60  # 10 minutes in seconds

async def send_request(url, data, headers):
    try:
        async with aiohttp.ClientSession() as session:
            await session.post(url, json=data, headers=headers)
        print("Request sent successfully")
    except Exception as e:
        print(f"Error sending request: {str(e)}")

async def delayed_launch(endpoint_url, payload, headers):
    await asyncio.sleep(5)  # Wait for 10 seconds
    await send_request(endpoint_url, payload, headers)

def handle_twilio_error(e: Exception) -> str:
    """Handle Twilio errors and return appropriate French error message."""
    if isinstance(e, TwilioRestException):
        if e.code == 20003:  # Authentication Error
            return "Une erreur d'authentification est survenue. Veuillez réessayer plus tard."
        elif e.code == 20404:  # Resource not found
            return "La ressource demandée n'a pas été trouvée."
        elif e.code == 21211:  # Invalid phone number
            return "Le numéro de téléphone fourni n'est pas valide."
        elif e.code == 21608:  # Message delivery failed
            return "L'envoi du message a échoué. Veuillez réessayer plus tard."
        else:
            return f"Une erreur est survenue avec le service Twilio (Code: {e.code}). Veuillez réessayer plus tard."
    else:
        return "Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes."

@app.post("/start_bot/{flow_name}/{config_id}")
async def multi_start_bot(request: Request, flow_name: str, config_id: str, response_class=PlainTextResponse):
    try:
        data = {}
        try:
            form_data = await request.form()
            data = dict(form_data)
        except Exception as e:
            logger.error(f"Error parsing form data: {e}")
            resp = VoiceResponse()
            resp.say(language="fr-FR", message="Une erreur est survenue lors du traitement de votre appel. Veuillez réessayer plus tard.", rate="1.15")
            return PlainTextResponse(content=str(resp), media_type="application/xml")
    
        logger.info(f"Received call data: {data}")
        callId = data.get('CallSid')
        caller_phone = data.get('From')

        france_tz = pytz.timezone('Europe/Paris')
        current_time = datetime.now(france_tz)
        current_hour = current_time.hour

        # Time-based routing for config25
        if config_id == 'config25':
            # Get current time in France
            
            # If time is between 8:30 AM and 7:00 PM Monday through Saturday (0-5)
            if current_time.weekday() < 6 and (8 <= current_hour < 19 or (current_hour == 8 and current_time.minute >= 30)):
                # Forward to the specified number during business hours
                resp = VoiceResponse()
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )
            
        if config_id == 'config40':
            # If time is between 9:30 AM and 6:30 PM Monday through Friday (0-4)
            if (current_time.weekday() < 5 and 
                ((9 < current_hour < 18) or 
                 (current_hour == 9 and current_time.minute >= 30) or
                 (current_hour == 18 and current_time.minute <= 30))):
                # Forward to the specified number during business hours
                resp = VoiceResponse()
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )

        if config_id == 'config2':
            # Check if time is outside business hours (before 8:30 AM or after 6:00 PM) or on weekends
            if (current_time.weekday() >= 5 or  # Weekend (Saturday or Sunday)
                current_hour < 8 or  # Before 8:00 AM
                current_hour > 18 or  # After 6:00 PM
                (current_hour == 8 and current_time.minute < 30)):  # Before 8:30 AM
                # Play specific sound outside business hours
                resp = VoiceResponse()
                resp.play(url="https://vocca-daily.s3.us-east-1.amazonaws.com/repondeur_agyl.mp3")
                resp.dial('+***********')
                return PlainTextResponse(
                    content=str(resp),
                    media_type="application/xml"
                )

        # Check for anonymous call
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33', '+***********', '+***********']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        if not callId:
            logger.error("Missing CallSid in request")
            raise HTTPException(status_code=400, detail="Missing 'CallSid' in request")

        room = await create_room()
        logger.info(f"Created room: {room}")

        # Make request to Cerebrium endpoint
        endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/launch"
        payload = {
            "room_url": room["room_url"],
            "token": room["token"],
            "sipUri": room["sip_endpoint"],
            "callId": callId,
            "flow_name": flow_name,
            "config_id": config_id,  # Use the config_id from the URL parameter
            "caller_phone": caller_phone
        }
        headers = {
            "Content-Type": "application/json",
        }

        # Start the delayed launch task
        asyncio.create_task(delayed_launch(endpoint_url, payload, headers))

        resp = VoiceResponse()
        logger.debug("Creating TwiML response with intro music")
        try:
            resp.play(
                url="https://vocca-daily.s3.us-east-1.amazonaws.com/new-loading.mp3", 
                loop=10
            )

            twiml_response = str(resp)
            logger.debug(f"Generated TwiML: {twiml_response}")
            return PlainTextResponse(
                content=twiml_response,
                media_type="application/xml"
            )
        except Exception as e:
            logger.error(f"Error generating TwiML: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    except TwilioRestException as e:
        logger.error(f"Twilio error: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message=handle_twilio_error(e), rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")
    except Exception as e:
        logger.error(f"Unexpected error in start_bot: {e}")
        resp = VoiceResponse()
        resp.say(language="fr-FR", message="Bonjour, le centre n'est actuellement pas disponible. Merci de réessayer dans les prochaines minutes.", rate="1.15")
        return PlainTextResponse(content=str(resp), media_type="application/xml")


async def create_room():
    params = DailyRoomParams(
        properties=DailyRoomProperties(
            exp=time.time() + MAX_SESSION_TIME,  # Add expiry time
            sip=DailyRoomSipParams(
                display_name="sip-dialin",
                video=False,
                sip_mode="dial-in",
                num_endpoints=1,
            )
        )
    )

    # Create sip-enabled Daily room via REST
    try:
        async with aiohttp.ClientSession() as session:
            daily_helper = DailyRESTHelper(
                daily_api_key=os.environ.get("DAILY_API_KEY"),
                daily_api_url="https://api.daily.co/v1",
                aiohttp_session=session
            )
            room: DailyRoomObject = await daily_helper.create_room(params=params)
            token = await daily_helper.get_token(room.url, MAX_SESSION_TIME)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unable to provision room {e}")

    print(f"Daily room returned {room.url} {room.config.sip_endpoint}")

    return {
        "room_url": room.url,
        "sip_endpoint": room.config.sip_endpoint,
        "token": token,
    }


def create_token(room_name: str):
    url = "https://api.daily.co/v1/meeting-tokens"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.environ.get('DAILY_TOKEN')}",
    }
    data = {"properties": {"room_name": room_name}}

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        token_info = response.json()
        return token_info
    else:
        logger.error(f"Failed to create token: {response.status_code}")
        return None


@app.post("/launch")
async def launch(request: Request):
    try:
        # Parse the JSON body
        data = await request.json()
        
        # Extract parameters from the request body
        room_url = data["room_url"]
        token = data["token"]
        sipUri = data["sipUri"]
        callId = data["callId"]
        flow_name = data["flow_name"]
        config_id = data["config_id"]
        caller_phone = data["caller_phone"]

        # Get the directory of the current script
        current_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(current_dir, f"{flow_name}.py")

        # Run the klarity_flow script with subprocess
        process = subprocess.Popen([
            sys.executable,
            script_path,  # Use the full path to the script
            "-u", room_url,
            "-t", token,
            "-i", callId,
            "-s", sipUri,
            "-c", config_id,
            "-p", caller_phone
        ])
        
        return {"status": "success", "message": "Klarity flow started"}
        
    except FileNotFoundError as e:
        logger.error(f"Script not found: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Script not found: {str(e)}")
    except KeyError as e:
        logger.error(f"Missing required parameter: {str(e)}")
        raise HTTPException(status_code=422, detail=f"Missing required parameter: {str(e)}")
    except Exception as e:
        logger.error(f"Error running klarity flow: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error running klarity flow: {str(e)}")
    
    
@app.post("/cai_start_bot", response_class=PlainTextResponse)
async def cai_start_bot(request: Request):
    try:
        # Get initial form data
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        # Get the digits from the form data
        digits = data.get('Digits')
        
        if not digits:
            # If no digits, present the menu
            resp = VoiceResponse()
            gather = Gather(numDigits=1, action="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/cai_start_bot")
            gather.say(language="fr-FR",
                      message="Bonjour, bienvenue au centre d'imagerie CAI. Pour Mérignac Mondésir, appuyez sur 1. Pour Mérignac Sainte Croix, appuyez sur 2. Pour Saint Médard En Jalles, appuyez sur 3.",
                      rate="1.15")
            resp.append(gather)
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/cai_start_bot")
            return str(resp)
        
        # Process the digits if present
        config_map = {'1': 'config17', '2': 'config18', '3': 'config19'}
        config = config_map.get(digits)

        if config:
            room = await create_room()
            
            # Call launch endpoint
            endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/launch"
            payload = {
                "room_url": room["room_url"],
                "token": room["token"],
                "sipUri": room["sip_endpoint"],
                "callId": data.get('CallSid'),
                "flow_name": "cai_flow_2",
                "config_id": config,
                "caller_phone": caller_phone
            }
            headers = {
                "Content-Type": "application/json",
            }

            asyncio.create_task(delayed_launch(endpoint_url, payload, headers))

            resp = VoiceResponse()
            resp.play(
                url="https://vocca-daily.s3.us-east-1.amazonaws.com/new-loading.mp3", 
                loop=10
            )

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error in cai_start_bot: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

    
    
@app.post("/optimaz_start_bot", response_class=PlainTextResponse)
async def optimaz_start_bot(request: Request):
    try:
        # Get initial form data
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        # Get the digits from the form data
        digits = data.get('Digits')
        
        if not digits:
            # If no digits, present the menu
            resp = VoiceResponse()
            gather = Gather(numDigits=1, action="https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/optimaz_start_bot")
            gather.say(language="fr-FR",
                      message="Bonjour, bienvenue au centre d'imagerie Optimaze de Nice. Pour les I R M et les Scanners, appuyez sur 1. Pour tout autre sujet, appuyez sur 2.",
                      rate="1.15")
            resp.append(gather)
            resp.redirect("https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/optimaz_start_bot")
            return str(resp)
        
        # Process the digits if present
        if digits == '1':
            # Forward to reception
            resp = VoiceResponse()
            resp.dial('+33423320066')  # Replace with actual reception phone number
            return str(resp)
        elif digits == '2':
            # Launch the bot
            room = await create_room()
            
            # Call launch endpoint
            endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/launch"
            payload = {
                "room_url": room["room_url"],
                "token": room["token"],
                "sipUri": room["sip_endpoint"],
                "callId": data.get('CallSid'),
                "flow_name": "opticim_flow",
                "config_id": "config21",
                "caller_phone": caller_phone
            }
            headers = {
                "Content-Type": "application/json",
            }

            asyncio.create_task(delayed_launch(endpoint_url, payload, headers))

            resp = VoiceResponse()
            resp.play(
                url="https://vocca-daily.s3.us-east-1.amazonaws.com/new-loading.mp3", 
                loop=10
            )

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error in optimaz_start_bot: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
@app.post("/primary_flow/{config_id}", response_class=PlainTextResponse)
async def primary_start_bot(request: Request, config_id: str):
    try:
        data = await request.form()
        logger.info(f"Received call data: {data}")
        
        # Check for anonymous call
        caller_phone = data.get('From')
        if caller_phone in ['anonymous', '+anonymous', '+***********', 'Restricted', 'Anonymous', '33']:
            resp = VoiceResponse()
            resp.say(language="fr-FR",
                    message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                    rate="1.15")
            resp.hangup()
            return str(resp)

        callId = data.get('CallSid')
        if not callId:
            raise HTTPException(status_code=500, detail="Missing 'CallSid' in request")
        
        # Customize based on config_id
        if config_id == "config52":
            sms_content = "Voici nos liens utiles pour prendre rendez-vous ou discuter avec le cabinet : https://linktr.ee/primarycauderan Pour toute urgence, contactez le 15"
            sound_url = "https://vocca-daily.s3.us-east-1.amazonaws.com/primary_cauderan.mp3"
            sender_name = "Primary"
        elif config_id == "config95":
            sms_content = "Voici nos liens utiles pour prendre rendez-vous ou discuter avec le cabinet : https://linktr.ee/primarychartrons Pour toute urgence, contactez le 15"
            sound_url = "https://vocca-daily.s3.us-east-1.amazonaws.com/primay_chartrons_3.mp3"
            sender_name = "Primary"
        elif config_id == "config138":
            sms_content = "Voici nos liens utiles pour prendre rendez-vous ou discuter avec le cabinet : https://linktr.ee/PrimaryBassinsaflot Pour toute urgence, contactez le 15"
            sound_url = "https://vocca.s3.amazonaws.com/audio/audio_bassinaflot-20250623-090652.mp3"
            sender_name = "Primary"
        elif config_id == "config139":
            sms_content = "Voici nos liens utiles pour prendre rendez-vous ou discuter avec le cabinet : https://linktr.ee/PrimarySainteCroix Pour toute urgence, contactez le 15"
            sound_url = " https://vocca.s3.amazonaws.com/audio/audio_saintecroix-20250623-091007.mp3"
            sender_name = "Primary"
        
        webhook_url = "https://n8n-self-hosted-vocca.onrender.com/webhook/46ace88a-b96e-4900-8dc3-4e5210f69d53"
        payload = {
            "to": "" + caller_phone,
            "content": sms_content,
            "from": sender_name
        }
        
        # Create task but don't await it
        asyncio.create_task(asyncio.to_thread(requests.post, webhook_url, json=payload))

        # First, create the TwiML response to play the sound
        resp = VoiceResponse()
        resp.play(url=sound_url)
        
        async def delayed_launch():
            await asyncio.sleep(31)  # Wait for sound to complete
            room = await create_room()
            
            # Call launch endpoint
            endpoint_url = "https://api.cortex.cerebrium.ai/v4/p-7f7215fa/voice-bot-4/launch"
            payload = {
                "room_url": room["room_url"],
                "token": room["token"],
                "sipUri": room["sip_endpoint"],
                "callId": callId,
                "flow_name": "primary_flow",
                "config_id": config_id,
                "caller_phone": caller_phone
            }
            headers = {
                "Content-Type": "application/json",
            }
            await send_request(endpoint_url, payload, headers)

        # Start the delayed launch task
        asyncio.create_task(delayed_launch())
        
        # Add silent pause after sound
        resp.pause(length=80)

        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Error in primary_start_bot: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/wait", response_class=PlainTextResponse)
async def wait():
    try:
        resp = VoiceResponse()
        resp.play(
            url="https://vocca-daily.s3.amazonaws.com/all-wait.mp3", 
            loop=10
        )
        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
    except Exception as e:
        logger.error(f"Error in wait endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
@app.post("/after_recall", response_class=PlainTextResponse)
async def after_recall(request: Request):
    try:
        # Get form data
        data = await request.form()
        caller_phone = data.get('From')
        
        # Initialize Supabase client with service role key
        supabase_url = os.environ.get("SUPABASE_URL")
        service_role_key = os.environ.get("SERVICE_ROLE_KEY")  # Using SERVICE_ROLE_KEY environment variable
        supabase = create_client(supabase_url, service_role_key)
                
        # Try querying the tables
        try:
            recalls_check = supabase.table("recalls").select("*").execute()
            
            if recalls_check.data:
                # Update recalls table
                recalls_update = supabase.table("recalls").update({"status": True, "confirmed": True}).eq("phone_number", caller_phone).execute()
                
                # Get appointment data for Doctolib update
                for recall in recalls_check.data:
                    if recall.get("phone_number") == caller_phone:
                        config = recall.get("config")
                        appt_id = recall.get("appt_id")
                        
                        if config and appt_id:
                            # Make PUT request to Doctolib API
                            doctolib_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/modify-appt"
                            doctolib_payload = {
                                "config": config,
                                "id": appt_id,
                                "note": "Rendez-vous confirmé par Vocca suite à un rappel du patient"
                            }
                            doctolib_headers = {
                                "Content-Type": "application/json",
                                "Authorization": f"Bearer {os.environ.get('SECRET_DOCTOLIB')}"
                            }
                            
                            try:
                                doctolib_response = requests.put(doctolib_url, json=doctolib_payload, headers=doctolib_headers)
                                logger.info(f"Doctolib API response: {doctolib_response.status_code} - {doctolib_response.text}")
                            except Exception as e:
                                logger.error(f"Error making Doctolib API request: {str(e)}")
        except Exception as e:
            logger.error(f"Error with recalls table: {str(e)}")

        
        # Create TwiML response
        resp = VoiceResponse()
        for _ in range(3):  # Will repeat 3 times
            resp.say(language="fr-FR",
                    message="Bonjour, votre rendez-vous médical est bien confirmé. Merci de ne pas oublier de vous rendre au centre à la date et heure prévue. À bientôt et merci.",
                    rate="1.1")
            resp.pause(length=5)
        
        return PlainTextResponse(
            content=str(resp),
            media_type="application/xml"
        )
    except Exception as e:
        logger.error(f"Error in after_recall endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")