HARDCODED SPEECH CONTENT - BOT VOCAL OUTPUT
==============================================

This file contains all hardcoded sentences and phrases that the bot speaks, extracted from the /src directory (excluding /src/customers).

## _say_and_wait Method Calls

### src/nodes/base/intake_processor.py

Line 135: "En cas d'urgence vitale, veuillez raccrocher immédiatement et composer le 15. Dans les autres cas, merci de poursuivre."
- Context: forward_call method, when phone_caller.intent == Intents.URGENCE

Line 154: "Toutes nos lignes sont occupés, je notifie le centre médical de votre appel."
- Context: forward_call method, when all_in_task is True or no forward_number

Line 167: "Je vais vous transférer vers le secrétariat. Il est possible que tout le personnel d'accueil soit momentanément indisponible et que l'appel se termine sans réponse. Si c'est le cas, n'hésitez pas à réessayer un peu plus tard."
- Context: forward_call method, before transferring call

Line 265: {msg} (variable content)
- Context: end_call method, saying goodbye message

Line 301: "Je vous remercie pour votre appel. Je vous souhaite une excellente journée !"
- Context: handle_last_question method, final goodbye

### src/nodes/appointment_form/intake_processor.py

Line 243: "J'informe le centre médical de votre demande."
- Context: start_node method, when intent is MODIFIER and appointment has passed

Line 261: "Un instant s'il-vous-plaît, je cherche votre rendez-vous."
- Context: start_node method, when intent is MODIFIER

Line 275-277: "Je suis désolé, je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when motive not found for MODIFIER

Line 347: {message} (variable content)
- Context: start_node method, dynamic message based on appointment details

Line 365-367: "Je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when intent is ANNULATION and is_new_patient

Line 386-388: "Je n'ai pas trouvé votre rendez-vous. Je notifie le centre médical, vous serez recontacté sous peu de temps."
- Context: start_node method, when appointment_form not found for ANNULATION

Line 415: "Je notifie le centre médical de votre confirmation de rendez-vous."
- Context: start_node method, when intent is CONFIRMER and is_new_patient

Line 429-431: "Je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when intent is CONFIRMER and appointment has passed

### src/nodes/appointment_form/handler_functions/handle_how_long_late.py

Line 165: "En raison d'un retard supérieur à {late_limit_time} minutes, le centre est malheureusement contraint d'annuler votre examen. Nous vous invitons à reprendre rendez-vous à un autre moment. Merci de votre compréhension."
- Context: handle_how_long_late function, when lateness exceeds limit

Line 186: "Si votre retard est supérieur à {late_limit_time} minutes, le centre devra malheureusement être contraint d'annuler votre examen."
- Context: handle_how_long_late function, warning about lateness

Line 193: "Je notifie votre retard au secrétariat"
- Context: handle_how_long_late function, default notification

### src/nodes/customer_base/intake_processor.py

Line 151: "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
- Context: handle_check_intent method, when intent not recognized

Line 161: {intent_script} (variable content from utils.tts.get_intent_sentence)
- Context: handle_check_intent method, speaking intent confirmation

## TTSSpeakFrame Usage

### src/processors/__init__.py

Lines 200-202: Random selection from:
- " Je suis là si vous avez besoin. On continue ensemble quand vous êtes prêt. "
- " Pas de souci si vous prenez un moment, je reste avec vous. Dites-moi quand vous êtes prêt. "
- " Vous êtes toujours là ? Je suis là si jamais vous avez besoin d'un petit coup de main. "
- Context: init_user_idle function, user idle callback

### src/utils/tts.py

Line 36: {text} (variable content)
- Context: say_something function, generic text-to-speech utility

## Localized Content

### src/locales/fr.py

Line 3: "Je notifie le centre médical. Si le retard est trop important, il est possible que le rendez-vous soit annulé par le médecin."
- Context: LATE_APPOINTMENT_MESSAGE constant

## Variable Speech Content (Dynamic)

### src/nodes/base/intake_processor.py

Lines 281-283: Dynamic message for leaving a note:
- "Pas de problème, quel est le message que vous souhaitez laisser ?"
- "Pas de problème, merci de préciser votre prénom et nom de famille puis merci de laisser votre message."

Lines 290-294: Dynamic prompt content:
- "Dit: {msg}"
- "Une fois que l'utilisateur a dit tout ce qu'il voulait, demande s'il souhaite ajouter quelque chose d'autre."
- "Si le patient a tout dit, dit 'Merci pour votre appel. Je vous souhaite une excellente journée !'"

## Additional Speech Content from Contact Form Prompts

### src/nodes/contact_form/prompts.py

Lines 26-32: Dynamic prompts with speech content:
- "Dit: '{first_sentence} Êtes-vous déjà venu à notre établissement ?'"
- Context: ask_new_or_existing_patient method

Lines 56-60: Dynamic prompts with speech content:
- "Dit: '{first_sentence} Pouvez-vous dire et épeller votre prénom ?'"
- Context: ask_first_name method

## Additional Speech Content from Appointment Form Prompts

### src/nodes/appointment_form/prompts.py

Line 313: "Dis : '{sentence}'"
- Context: ask_doctor_name method, asking about doctor preference

Line 505: "Dit: Voici les horaires disponibles: {' ou '.join(dates_str)}"
- Context: suggest_appointment_time method, presenting available times

Line 535: "Dit: Voici nos horaires disponibles les plus proche: {' ou '.join(dates_str)}"
- Context: suggest_appointment_time method, presenting closest available times

Line 675: "Dit: Voulez-vous confirmer votre rendez-vous du {appointment_str} ?"
- Context: ask_confirm_appointment_metadata method

Line 718: "Dit: 'Avant que je puisse confirmer votre rendez-vous, pouvez-vous me dire pourquoi vous souhaitez prendre {motive_name}?'"
- Context: ask_more_information_for_motive method

Line 741: "Dit: 'Est-ce qu'une {motif_name} est également demandé sur votre ordonnance?'"
- Context: ask_with_echo_mammaire method

Line 757: "Dit: '{condition_msg}'"
- Context: ask_motive_condition method

Line 773: "Dit: 'Le Dr {doctor_name} n'a malheureusement pas de disponibilité plus tôt. Souhaitez-vous prendre RDV avec un autre praticien du centre pour une date plus proche ?'"
- Context: ask_continue_with_same_doctor method

Line 800: "👉 Dis : **\"{sentence}\"**"
- Context: ask_how_long_late method, asking about lateness duration

Lines 854-864: Dynamic speciality prompts:
- "Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {example_str} ?"
- "Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {specialities_to_ask_str} ?"
- "Dit: Pour quelle spécialité appelez-vous : {specialities_to_ask_str} ?"
- Context: ask_speciality method

Line 908: "Dit: 'Souhaitez-vous un seul ou deux examens ?'"
- Context: ask_how_many_motives method

## Confirmation and Question Phrases

### Exact phrases for appointment confirmation:
- "Confirmez-vous votre {intent} rendez-vous du {date_str} pour vos deux examens ?"
- "Confirmez-vous votre {intent} rendez-vous du {appointment_text_time} pour {appointment.visit_motive_name} ?"

### Exact phrases for reason inquiries:
- "Pour quelle raison souhaitez-vous {intent.value} {vos} rendez-vous ?"

### Exact phrases for identity confirmation:
- "Afin de poursuivre j'ai besoin de confirmer votre identité. Est-ce que votre appel est pour {patient_data.first_name} {patient_data.last_name} ?"

### Exact phrases for availability:
- "Dites-moi simplement à quel moment vous seriez disponible, je vais m'adapter !"

## Notes

- Many speech outputs are generated dynamically using variables and templates
- Intent-specific scripts are retrieved using utils.tts.get_intent_sentence()
- Some content includes variable substitution for personalization (names, dates, times)
- Error messages and fallback responses are hardcoded for consistency
- User idle messages are randomized to provide variety
- Exact phrasing is enforced in many prompts with strict instructions
- Speech content is often embedded within prompt instructions using "Dit:" or "Dis:" prefixes

## Additional Utility Functions

### src/utils/tts.py

Line 6-15: Email conversion function for speech:
- Function: convert_email_to_spoken_text(email)
- Converts email addresses to spoken French text
- Special characters mapping: {"@": "arobase", ".": "point", "-": "tiret", "_": "tiret du bas"}
- Context: Used for speaking email addresses aloud

### src/utils/time.py

Line 23: Date/time conversion for speech:
- Template: "{weekday} {day} {month} à {hour} heures"
- Function: convert_utc_to_french_text(utc_date_str)
- Converts UTC timestamps to spoken French date/time format
- Context: Used for speaking appointment dates and times

## Configuration-Based Speech Content

### src/locales/fr.py

Line 2: "Le patient a signalé un retard au téléphone par Vocca"
- Context: LATE_APPOINTMENT_NOTE constant for system notes

Line 3: "Je notifie le centre médical. Si le retard est trop important, il est possible que le rendez-vous soit annulé par le médecin."
- Context: LATE_APPOINTMENT_MESSAGE constant for lateness warnings

## Demo Bot Speech Content (Reference)

### Demo prompts showing speech patterns:

Line 21-22: "Dit: Salut! Je suis une demonstration d'un assistant vocal. Si tu veux me tester, demande moi l'heure ou dis moi ton prénom."
- Context: Demo bot initialization message

Line 40: "Dis: Il est {current_time}."
- Context: Demo time announcement

Line 51: "Dit: Salut {first_name}! C'est un très jolie prénom! Veux-tu me demander l'heure?"
- Context: Demo personalized greeting

## Constants and Configuration Text

### Instructions for early arrival:
- "Merci de vous présenter 20 minutes avant l'heure du rendez-vous muni de votre carte Vitale et mutuelle."
- Context: come_early_instructions in configuration

## Summary of Speech Content Categories

1. **Emergency Messages**: Urgent care instructions and emergency number references
2. **Transfer Messages**: Call forwarding and transfer notifications
3. **Appointment Messages**: Booking confirmations, modifications, cancellations
4. **Error Messages**: Fallback responses when system cannot understand or process requests
5. **Greeting Messages**: Welcome and goodbye phrases
6. **Idle Messages**: Prompts when user is inactive
7. **Identity Confirmation**: Patient verification phrases
8. **Time/Date Announcements**: Spoken date and time formats
9. **Email Announcements**: Spoken email address formats
10. **Instruction Messages**: Procedural guidance for patients

## COMPREHENSIVE ADDITIONAL SPEECH CONTENT FOUND

### src/nodes/appointment_form/handler_functions/handle_check_for_mri_counter_indications.py

Line 27: "Pour votre sécurité, si vous portez un objet métallique, je ne suis pas habilité à effectuer la prise de rendez-vous pour une IRM. Pas d'inquiétude, je note votre demande et vous serez recontacté dans les plus brefs délais."
- Context: handle_check_for_mri_counter_indications function, when patient has MRI counter-indications

### src/nodes/appointment_form/handler_functions/handle_confirm_motive.py

Line 73: "Je suis désolé, je n'ai pas trouvé le motif que vous souhaitez"
- Context: handle_confirm_motive function, when max retries exceeded

Line 91: "Si vous ne souhaitez plus votre rendez-vous, vous pouvez l'annuler avec un autre appel. Je vais maintenant vous prendre un nouveau rendez-vous."
- Context: handle_confirm_motive function, when user doesn't confirm and intent is MODIFIER

Line 145: "Je suis désolé, le patient doit avoir au moins {age_str} ans pour prendre rendez-vous."
- Context: handle_confirm_motive function, when patient is too young

Line 157: "Je suis désolé, seul les patients de moins de {age_str} ans peuvent prendre rendez-vous."
- Context: handle_confirm_motive function, when patient is too old

Line 169: "Ce motif n'est pas disponible pour les hommes."
- Context: handle_confirm_motive function, when motive is female-only

Line 183: "Malheureusement, nous ne pouvons pas fixer de rendez-vous pour ce motif pour le moment."
- Context: handle_confirm_motive function, for specific config and motive restrictions

Line 204: "Merci d'envoyer un mail à {email} avec vos coordonnées en demandant une consultation de suivi"
- Context: handle_confirm_motive function, for specific config87 motive

Line 221: "Je suis désolé, mais nous ne pouvons pas prendre de rendez-vous pour les nouveaux patients dans cette spécialité."
- Context: handle_confirm_motive function, when new patients not allowed for specialty

Line 244: "Pour ce type de demande, il n'est malheureusement pas possible de finaliser la prise de rendez-vous par téléphone. Votre demande a bien été prise en compte, et un membre de notre équipe d'accueil vous recontactera dans les plus brefs délais."
- Context: handle_confirm_motive function, when motive is not open for online booking

Line 257: "Je notifie Docteur {self.appointment_form.medecin} de votre demande."
- Context: handle_confirm_motive function, when notifying specific doctor

Line 297: "Nous vous informons qu'en raison d'une forte demande, les plages de cette consultation sont actuellement très limitées. Si aucun créneau ne peut être proposé au cours de cet appel, nous vous recommandons de consulter régulièrement Doctolib, où les disponibilités libérées sont immédiatement réaffichées."
- Context: handle_confirm_motive function, for high-demand consultations

Line 363: "Il n'est malheureusement pas possible de réaliser {forbidden_motive} le même jour. Nous allons donc vous transférer vers le secrétariat afin qu'un membre de l'accueil puisse vous aider à planifier ces deux rendez-vous sur des créneaux distincts."
- Context: handle_confirm_motive function, when motives cannot be combined

Line 450: "Le Docteur {doctor.name.lower().split('(')[0].strip()} n'a pas de créneaux disponibles pour le moment. Je vais vérifier les autres médecins disponibles."
- Context: handle_confirm_motive function, when specific doctor has no availability

Line 491: "Je suis désolé mais il n'y a pas de rendez-vous disponible actuellement. Je vous invite à rappeler plus tard de nouveaux créneaux ouvrent chaque jour."
- Context: handle_confirm_motive function, when no appointments available

### src/nodes/appointment_form/handler_functions/handle_confirm_appointment_metadata.py

Line 48: "Votre rendez-vous est confirmé. {instruction_msg}"
- Context: handle_confirm_appointment_metadata function, when appointment confirmed

### src/nodes/appointment_form/handler_functions/handle_appointment_confirmation.py

Line 41: "Un instant, je réserve {vos} rendez-vous."
- Context: handle_appointment_confirmation function, when creating new booking

Line 60: "{vos} rendez-vous {sont} confirmés."
- Context: handle_appointment_confirmation function, when appointments confirmed

Line 88: "Un instant, je modifie votre rendez-vous."
- Context: handle_appointment_confirmation function, when modifying appointment

Line 102: "Votre {intent} est confirmé."
- Context: handle_appointment_confirmation function, when modification confirmed

Line 180: "{vos} rendez-vous {sont} bien annulé"
- Context: handle_appointment_confirmation function, when appointments cancelled

Line 225: "Désolé, un problème est survenu lors de la prise de rendez-vous. Je vais laisser une note au secrétariat."
- Context: handle_appointment_confirmation function, error handling

### src/nodes/appointment_form/handler_functions/handle_appointment_datetime.py

Line 164: "Je vérifie nos disponibilités les plus proches du {dt_str}." / "Je vérifie nos disponibilités les plus proches du {dt_str.split(' ')[0]} {'matin' if wish_dt.hour < 12 else 'après-midi'} {' '.join(dt_str.split(' ')[1:])}."
- Context: handle_appointment_datetime function, when checking availability

Line 203: "Il n'y a pas de créneaux disponibles pour le {dt_str}"
- Context: handle_appointment_datetime function, when no slots available

Line 368: "Si vous portez des lentilles souples, enlevez-les 24 heures avant la consultation."
- Context: handle_appointment_datetime function, for specific config77 motive
