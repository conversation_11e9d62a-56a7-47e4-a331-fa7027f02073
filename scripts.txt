HARDCODED SPEECH CONTENT - BOT VOCAL OUTPUT
==============================================

This file contains all hardcoded sentences and phrases that the bot speaks, extracted from the /src directory (excluding /src/customers).

## _say_and_wait Method Calls

### src/nodes/base/intake_processor.py

Line 135: "En cas d'urgence vitale, veuillez raccrocher immédiatement et composer le 15. Dans les autres cas, merci de poursuivre."
- Context: forward_call method, when phone_caller.intent == Intents.URGENCE

Line 154: "Toutes nos lignes sont occupés, je notifie le centre médical de votre appel."
- Context: forward_call method, when all_in_task is True or no forward_number

Line 167: "Je vais vous transférer vers le secrétariat. Il est possible que tout le personnel d'accueil soit momentanément indisponible et que l'appel se termine sans réponse. Si c'est le cas, n'hésitez pas à réessayer un peu plus tard."
- Context: forward_call method, before transferring call

Line 265: {msg} (variable content)
- Context: end_call method, saying goodbye message

Line 301: "Je vous remercie pour votre appel. Je vous souhaite une excellente journée !"
- Context: handle_last_question method, final goodbye

### src/nodes/appointment_form/intake_processor.py

Line 243: "J'informe le centre médical de votre demande."
- Context: start_node method, when intent is MODIFIER and appointment has passed

Line 261: "Un instant s'il-vous-plaît, je cherche votre rendez-vous."
- Context: start_node method, when intent is MODIFIER

Line 275-277: "Je suis désolé, je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when motive not found for MODIFIER

Line 347: {message} (variable content)
- Context: start_node method, dynamic message based on appointment details

Line 365-367: "Je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when intent is ANNULATION and is_new_patient

Line 386-388: "Je n'ai pas trouvé votre rendez-vous. Je notifie le centre médical, vous serez recontacté sous peu de temps."
- Context: start_node method, when appointment_form not found for ANNULATION

Line 415: "Je notifie le centre médical de votre confirmation de rendez-vous."
- Context: start_node method, when intent is CONFIRMER and is_new_patient

Line 429-431: "Je n'ai pas trouvé votre rendez-vous. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps."
- Context: start_node method, when intent is CONFIRMER and appointment has passed

### src/nodes/appointment_form/handler_functions/handle_how_long_late.py

Line 165: "En raison d'un retard supérieur à {late_limit_time} minutes, le centre est malheureusement contraint d'annuler votre examen. Nous vous invitons à reprendre rendez-vous à un autre moment. Merci de votre compréhension."
- Context: handle_how_long_late function, when lateness exceeds limit

Line 186: "Si votre retard est supérieur à {late_limit_time} minutes, le centre devra malheureusement être contraint d'annuler votre examen."
- Context: handle_how_long_late function, warning about lateness

Line 193: "Je notifie votre retard au secrétariat"
- Context: handle_how_long_late function, default notification

### src/nodes/customer_base/intake_processor.py

Line 151: "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
- Context: handle_check_intent method, when intent not recognized

Line 161: {intent_script} (variable content from utils.tts.get_intent_sentence)
- Context: handle_check_intent method, speaking intent confirmation

## TTSSpeakFrame Usage

### src/processors/__init__.py

Lines 200-202: Random selection from:
- " Je suis là si vous avez besoin. On continue ensemble quand vous êtes prêt. "
- " Pas de souci si vous prenez un moment, je reste avec vous. Dites-moi quand vous êtes prêt. "
- " Vous êtes toujours là ? Je suis là si jamais vous avez besoin d'un petit coup de main. "
- Context: init_user_idle function, user idle callback

### src/utils/tts.py

Line 36: {text} (variable content)
- Context: say_something function, generic text-to-speech utility

## Localized Content

### src/locales/fr.py

Line 3: "Je notifie le centre médical. Si le retard est trop important, il est possible que le rendez-vous soit annulé par le médecin."
- Context: LATE_APPOINTMENT_MESSAGE constant

## Variable Speech Content (Dynamic)

### src/nodes/base/intake_processor.py

Lines 281-283: Dynamic message for leaving a note:
- "Pas de problème, quel est le message que vous souhaitez laisser ?"
- "Pas de problème, merci de préciser votre prénom et nom de famille puis merci de laisser votre message."

Lines 290-294: Dynamic prompt content:
- "Dit: {msg}"
- "Une fois que l'utilisateur a dit tout ce qu'il voulait, demande s'il souhaite ajouter quelque chose d'autre."
- "Si le patient a tout dit, dit 'Merci pour votre appel. Je vous souhaite une excellente journée !'"

## Additional Speech Content from Contact Form Prompts

### src/nodes/contact_form/prompts.py

Lines 26-32: Dynamic prompts with speech content:
- "Dit: '{first_sentence} Êtes-vous déjà venu à notre établissement ?'"
- Context: ask_new_or_existing_patient method

Lines 56-60: Dynamic prompts with speech content:
- "Dit: '{first_sentence} Pouvez-vous dire et épeller votre prénom ?'"
- Context: ask_first_name method

## Additional Speech Content from Appointment Form Prompts

### src/nodes/appointment_form/prompts.py

Line 313: "Dis : '{sentence}'"
- Context: ask_doctor_name method, asking about doctor preference

Line 505: "Dit: Voici les horaires disponibles: {' ou '.join(dates_str)}"
- Context: suggest_appointment_time method, presenting available times

Line 535: "Dit: Voici nos horaires disponibles les plus proche: {' ou '.join(dates_str)}"
- Context: suggest_appointment_time method, presenting closest available times

Line 675: "Dit: Voulez-vous confirmer votre rendez-vous du {appointment_str} ?"
- Context: ask_confirm_appointment_metadata method

Line 718: "Dit: 'Avant que je puisse confirmer votre rendez-vous, pouvez-vous me dire pourquoi vous souhaitez prendre {motive_name}?'"
- Context: ask_more_information_for_motive method

Line 741: "Dit: 'Est-ce qu'une {motif_name} est également demandé sur votre ordonnance?'"
- Context: ask_with_echo_mammaire method

Line 757: "Dit: '{condition_msg}'"
- Context: ask_motive_condition method

Line 773: "Dit: 'Le Dr {doctor_name} n'a malheureusement pas de disponibilité plus tôt. Souhaitez-vous prendre RDV avec un autre praticien du centre pour une date plus proche ?'"
- Context: ask_continue_with_same_doctor method

Line 800: "👉 Dis : **\"{sentence}\"**"
- Context: ask_how_long_late method, asking about lateness duration

Lines 854-864: Dynamic speciality prompts:
- "Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {example_str} ?"
- "Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {specialities_to_ask_str} ?"
- "Dit: Pour quelle spécialité appelez-vous : {specialities_to_ask_str} ?"
- Context: ask_speciality method

Line 908: "Dit: 'Souhaitez-vous un seul ou deux examens ?'"
- Context: ask_how_many_motives method

## Confirmation and Question Phrases

### Exact phrases for appointment confirmation:
- "Confirmez-vous votre {intent} rendez-vous du {date_str} pour vos deux examens ?"
- "Confirmez-vous votre {intent} rendez-vous du {appointment_text_time} pour {appointment.visit_motive_name} ?"

### Exact phrases for reason inquiries:
- "Pour quelle raison souhaitez-vous {intent.value} {vos} rendez-vous ?"

### Exact phrases for identity confirmation:
- "Afin de poursuivre j'ai besoin de confirmer votre identité. Est-ce que votre appel est pour {patient_data.first_name} {patient_data.last_name} ?"

### Exact phrases for availability:
- "Dites-moi simplement à quel moment vous seriez disponible, je vais m'adapter !"

## Notes

- Many speech outputs are generated dynamically using variables and templates
- Intent-specific scripts are retrieved using utils.tts.get_intent_sentence()
- Some content includes variable substitution for personalization (names, dates, times)
- Error messages and fallback responses are hardcoded for consistency
- User idle messages are randomized to provide variety
- Exact phrasing is enforced in many prompts with strict instructions
- Speech content is often embedded within prompt instructions using "Dit:" or "Dis:" prefixes
