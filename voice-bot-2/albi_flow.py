import asyncio
from typing import Any, Dict, List
import aiohttp
import os
import sys
import argparse
import wave
import requests
from datetime import datetime, timedelta

from pipecat.services.openai import OpenAILLMContextFrame

from pipecat.processors.frame_processor import FrameDirection

from timing_config import timing_config

from loguru import logger
from dotenv import load_dotenv

from datetime import datetime
from utils import convert_utc_to_french_text, convert_email_to_spoken_text, is_dst

from base_intake_processor import BaseIntakeProcessor

import pytz
from supabase import create_client

import openai

global configContent

# Load environment variables
load_dotenv(override=True)

# Configure logger
logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

daily_api_key = os.getenv("DAILY_API_KEY", "")

from sound_loader import load_sounds

# Load sound files
sounds = load_sounds()

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

# Initialize OpenAI
openai.api_key = os.getenv("OPENAI_API_KEY", "")


# Function to infer gender from first name
async def infer_gender_from_name(first_name):
    try:
        client = openai.OpenAI()
        response = client.chat.completions.create(
            model="gpt-4.1-nano",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are a helpful assistant that determines the likely gender"
                        " of a French first name. Respond with exactly one letter: 'M'"
                        " for male or 'F' for female."
                    ),
                },
                {
                    "role": "user",
                    "content": (
                        "What is the likely gender of the French first name"
                        f" '{first_name}'? Respond with only 'M' or 'F'."
                    ),
                },
            ],
            max_tokens=1,
        )
        gender = response.choices[0].message.content.strip()
        logger.debug(f"Inferred gender for {first_name}: {gender}")
        # Ensure the response is either 'M' or 'F'
        if gender not in ["M", "F"]:
            gender = "M"  # Default to male if response is unexpected
        return gender
    except Exception as e:
        logger.error(f"Error inferring gender: {str(e)}")
        return "M"  # Default to male in case of any error


# SMS Pour l'envoi de résultats :

# Vos resultats medicaux sont disponibles sur : bit.ly/Optimaz
# La Reference Rendez-Vous se trouve dans un email envoye precedemment, verifiez vos spams

try:
    supabase = create_client(supabase_url, supabase_key)  # type: ignore
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {str(e)}")
    supabase = None


def lunch_break(availability: Dict[str, str], daylight_saving: int) -> bool:
    start_date = datetime.strptime(
        availability["start_date"], "%Y%m%dT%H:%M:%S.000Z"
    ) + timedelta(hours=daylight_saving)
    is_lunch_break = start_date.hour >= 12 and start_date.hour < 14
    return is_lunch_break


class AlbiIntakeProcessor(BaseIntakeProcessor):
    def __init__(self, context, llm, tts, callId, config, patient_data):
        super().__init__(context, llm, tts, callId, config, patient_data)
        self.configContent = configContent
        self._appointment_confirmed = False
        self._api_call_in_progress = False
        self._ask_date_called = False
        self.dst = 2 if is_dst() else 1
        self._availability_checked = False
        self.location_ids = []
        self.site_ids = []
        if self.configContent["config"] == "config56":
            self.location_ids = [19]
            self.site_ids = [2]
        else:
            self.location_ids = [12] if self.configContent["config"] == "config93" else [1, 2]
            self.site_ids = [2] if self.configContent["config"] == "config93" else [1]

    def _initialize_context(self):
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    f"Dis quelque chose comme  : 'Bienvenue au centre de radiologie"
                    f" {configContent['name']}. Comment puis-je vous aider ?' Une fois que tu as"
                    f" bien compris l'intention de l'utilisateur utilise la fonction"
                    f" check_intent. Si le patient souhaite parler au secrétariat, son"
                    f" intention est urgence. Si le patient souhaite une échographie ou une mammographie utilise la fonction forward_call."
                ),
            }
        )

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "check_intent",
                        "description": (
                            "utilise cette fonction lorsque tu as compris l'intention de"
                            " l'utilisateur. Si l'utilisateur souhaite parler à un médecin"
                            " son intention est question."
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "intent": {
                                    "type": "string",
                                    "description": "intention de l'utilisateur",
                                    "enum": [
                                        "rendez-vous",
                                        "question",
                                        "urgence",
                                        "retard",
                                        "annulation",
                                    ],
                                }
                            },
                            "required": ["intent"],
                        },
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le patient souhaite parler au secrétariat"
                        ),
                    },
                }
            ]
        )

    async def forward_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm,
        context,
        result_callback,
        forward_number=None,
    ):
        forward_number = forward_number or self.configContent["forward_number"]
        await super().forward_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            forward_number,
        )

    async def search_patient(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        await self.appt_unique_type(
            function_name,
            tool_call_id,
            {"new_patient": True},
            llm,
            context,
            result_callback,
        )

    async def check_intent(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        intent = args["intent"]

        if intent in ["retard", "annulation", "urgence"]:
            await self.forward_call(
                function_name, tool_call_id, args, llm, context, result_callback
            )
        elif intent in ["rendez-vous", "question"]:
            await self.search_patient(
                function_name, tool_call_id, args, llm, context, result_callback
            )

    async def search_patient(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        self._appt_data["instructions"] = ""
        if self._patient_data.get("first_name") != None:
            if args["intent"] == "rendez-vous":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "set_appt",
                                "description": (
                                    "utilise cette fonction si c'est la bonne identité"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "False si l'identité est correcte",
                                        }
                                    },
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "appt_type",
                                "description": (
                                    "utilise cette fonction si l'identité est incorrecte"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": (
                                                "True si l'identité est incorrecte"
                                            ),
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite parler au secrétariat"
                                ),
                            },
                        }
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            "Dis : Appelez-vous pour"
                            f" {self._patient_data['first_name']} {self._patient_data['last_name']} ?"
                            " Utilise la fonction set_appt si l'identité est correcte. Si"
                            " l'identité est incorrecte utilise la fonction appt_type. Ne demande pas le type de rendez-vous souhaité à cette étape."
                        ),
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "question":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": (
                                    "utilise cette fonction si l'identité est correcte"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "False si lutilisateur a dit oui",
                                        }
                                    },
                                    "required": ["new_patient"],
                                }
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": (
                                    "utilise cette fonction si l'utilisateur dit non"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite parler au secrétariat"
                                ),
                            },
                        }
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            "Dis : Appelez-vous pour"
                            f" {self._patient_data['first_name']} {self._patient_data['last_name']} ?"
                            " Une fois que l'utilisateur a répondu utilise la fonction"
                            " set_question."
                        ),
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "retard":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "retard",
                                "description": (
                                    "utilise cette fonction si l'identité est correcte"
                                ),
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": (
                                    "utilise cette fonction si l'utilisateur dit non"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite parler au secrétariat"
                                ),
                            },
                        }
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            "Dis : Appelez-vous pour"
                            f" {self._patient_data['first_name']} {self._patient_data['last_name']} ?"
                            " Une fois que l'utilisateur a répondu utilise la fonction"
                            " retard."
                        ),
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "annulation":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "appt_intent",
                                "description": (
                                    "utilise cette fonction si l'identité est correcte"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "intent": {
                                            "type": "string",
                                            "description": (
                                                "annuler si lidentité est correcte ou"
                                                " lutilisateur dit oui"
                                            ),
                                        }
                                    },
                                    "required": ["intent"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": (
                                    "utilise cette fonction si l'utilisateur dit non"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite parler au secrétariat"
                                ),
                            },
                        }
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            "Dis : Appelez-vous pour"
                            f" {self._patient_data['first_name']} {self._patient_data['last_name']} ?"
                            " Une fois que l'utilisateur a répondu utilise la fonction"
                            " appt_intent avec intent=annuler si c'est la bonne identité,"
                            " sinon utilise la fonction set_question."
                        ),
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

        else:
            if args["intent"] == "rendez-vous":
                await self.appt_type(
                    function_name,
                    tool_call_id,
                    {"new_patient": True},
                    llm,
                    context,
                    result_callback,
                )
            elif args["intent"] in ["question", "retard", "annulation"]:
                await self.set_question(
                    function_name,
                    tool_call_id,
                    {"new_patient": True},
                    llm,
                    context,
                    result_callback,
                )

    async def appt_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        
        # Always update _new_patient from args if provided
        if "new_patient" in args:
            self._new_patient = args["new_patient"]
        # Only set default if attribute doesn't exist
        elif not hasattr(self, '_new_patient'):
            self._new_patient = False

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_unique_type",
                        "description": (
                            "Utilise cette fonction si le patient confirme qu'il souhaite un seul type d'examen"
                        )
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le patient souhaite plusieurs examens différents"
                        ),
                    },
                },
            ]
        )
        
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    "Demandez au patient : Souhaitez-vous un seul type d'examen ou deux examens ? "
                    "S'il veut un seul examen, utilisez la fonction "
                    "appt_unique_type avec les paramètres appropriés. S'il veut plusieurs "
                    "examens différents utilisez la fonction forward_call directement. Pas besoin de demander le type d'examen à cette étape."
                ),
            }
        )
        
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def set_question(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        add_string = ""

        self._new_patient = args.get("new_patient", True)

        if self._new_patient:
            self._patient_data = {}

            add_string = (
                "Demande tout d'abord de dire et épeler le prénom, puis de dire et"
                " épeler le nom de famille, puis enfin la date de naissance du patient."
                " En trois étapes. sans parler du format"
            )

            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "recap_question",
                            "description": (
                                "utilise cette fonction si tu n'arrives pas à résoudre le"
                                " problème du patient"
                            ),
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "problem": {
                                        "type": "string",
                                        "description": "Le problème détaillé du patient",
                                    },
                                    "fullname": {
                                        "type": "string",
                                        "description": (
                                            "Le nom et le prénom du patient, doit"
                                            " correspondre à un prénom et un nom de famille"
                                            " crédibles français"
                                        ),
                                    },
                                    "birthdate": {
                                        "type": "string",
                                        "description": (
                                            "La date de naissance du patient au format"
                                            " DD/MM/YYYY"
                                        ),
                                    },
                                },
                                "required": ["problem"],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "appt_type",
                            "description": (
                                "utilise cette fonction lorsque le patient souhaite prendre"
                                " un rendez-vous"
                            ),
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "forward_call",
                            "description": (
                                "utilise cette fonction si le patient souhaite parler au"
                                " secrétariat"
                            ),
                        },
                    },
                ]
            )

        else:
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "recap_question",
                            "description": (
                                "utilise cette fonction si tu n'arrives pas à résoudre le"
                                " problème du patient"
                            ),
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "problem": {
                                        "type": "string",
                                        "description": "Le problème détaillé du patient",
                                    }
                                },
                                "required": ["problem"],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "appt_type",
                            "description": (
                                "utilise cette fonction lorsque le patient souhaite prendre"
                                " un rendez-vous"
                            ),
                        },
                    },
                    {
                        "type": "function",
                        "function": { 
                            "name": "forward_call",
                            "description": (
                                "utilise cette fonction si le patient souhaite parler au"
                                " secrétariat"
                            ),
                        },
                    },
                ]
            )

        self._context.add_message(
            {
                "role": "system",
                "content": (
                    add_string
                    + """Si tu peux répondre à la question, essaie d'aider le patient. Si tu n'as pas les informations nécessaires, propose de laisser une note au secrétariat. Pose des questions pour permettre au patient d'expliquer son problème.

                En cas d'urgence :
                Si le problème semble urgent, dis :
                "Si c'est une urgence vitale, n'hésitez pas à contacter l'hôpital le plus proche. Sinon, je peux vous transférer au secrétariat."

                Compréhension du problème :
                Si et seuleument si tu ne sais pas répondre à la question du patient, utilise la fonction recap_question.

                Prise de rendez-vous :
                Tu ne peux pas consulter les disponibilités ni prendre un rendez-vous à cette étape.
                Si le patient souhaite prendre un rendez-vous, utilise la fonction appt_type. Par exemple, si le patient dit "rendez-vous", demande :
                "Avez-vous une question sur un rendez-vous ou souhaitez-vous modifier, annuler ou prendre un rendez-vous ?"
                Dans ce cas, tu utiliseras la fonction appt_type.
                Si le patient dit "Je veux prendre rendez-vous", utilise également cette fonction.

                Transfert au secrétariat :
                Si le patient souhaite parler au secrétariat, à un humain ou à un conseiller, demande :
                "Souhaitez-vous que je vous transfère au secrétariat ?"
                Si le patient confirme ou dit "oui", utilise la fonction forward_call.

                Si le patient demande : "Quels sont les tarifs ?", réponds :
                "Le centre est conventionné secteur 1, mais il peut y avoir des dépassements d'honoraires."

                exemple de conversation :
                Réceptionniste : "Pouvez-vous m'expliquer votre question ou votre problème ?"
                Patient : "J'ai un problème de facturation."
                Réceptionniste : "Pouvez-vous m'en dire un peu plus sur votre problème de facturation afin que je puisse vous aider ?"
                Patient : "Voici mon problème : [exemple de problème]."

                À ce moment-là, réponds à son problème si les informations nécessaires sont disponibles dans le prompt.

                Si tu ne connais pas la réponse, ne l'invente pas et dis simplement :
                "J'ai bien compris votre problème et je peux laisser une note au secrétariat."

                Résultats :
                    •	Délais : Si des résultats doivent être transmis, ils seront envoyés sous 24 à 48 heures après le rendez-vous.
                    •	Consultation des résultats : Les documents sont disponibles sur votre fiche diffusion remise à la facturation. Un mail avec vos identifiants a également été envoyé (vérifiez dans vos spams). Informations supplémentaires sur le centre : """
                    + configContent["additional_information"]
                ),
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def set_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        
        if self._new_patient:
            await self.appt_type(
                function_name,
                tool_call_id,
                {"new_patient": True},
                llm,
                context,
                result_callback,
            )
            return

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_intent",
                        "description": (
                            "utilise cette fonction lorsque tu as compris l'intention de"
                            " l'utilisateur"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "intent": {
                                    "type": "string",
                                    "description": "intention de l'utilisateur",
                                    "enum": ["nouveau", "modifier", "annuler"],
                                }
                            },
                            "required": ["intent"],
                        },
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    "Parfait. Souhaitez-vous un nouveau rendez-vous annuler ou modifier un"
                    " rendez-vous ? Une fois que l'utilisateur a répondu utilise la"
                    " fonction appt_intent."
                ),
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_confirm(
        self, function_name, tool_call_id, args, llm, context, result_callback, intent
    ):

        await self.forward_call(
            function_name, tool_call_id, args, llm, context, result_callback
        )

    async def appt_unique_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        additional_info = args.get("additional_info", "")

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_check_type",
                        "description": (
                            "Utilise cette fonction lorsque tu as compris le type de"
                            " rendez-vous souhaité par le patient"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "visit_motive": {
                                    "type": "string",
                                    "description": "Le type de rendez-vous souhaité",
                                }
                            },
                            "required": ["visit_motive"],
                        },
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le patient souhaite deux rendez-vous"
                            " différents"
                        ),
                    },
                },
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    f"Dis: '{additional_info} Quel est le motif de rendez-vous inscrit sur"
                    " votre ordonnance ?' Si le patient réponds uniquement"
                    " radiogaphie, demande pour quelle partie du corps le patient souhaite"
                    " la radiographie. Le centre réalise des"
                    " mammographies. Une fois que le patient a répondu, utilise la"
                    " fonction appt_check_type si tu as bien compris le type de"
                    " rendez-vous souhaité par le patient. Si le patient souhaite deux"
                    " rendez-vous différents, comme échographie et mammographie par"
                    " exemple, utilise la fonction forward_call. Si le patient souhaite autre chose qu'une radiographie utilise la fonction forward_call. Pour échographie abdominale et radio de l'abdomen, pas besoin de demander la partie du corps au patient."
                ),
            }
        )

        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_check_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        async def make_api_call():
            try:
                api_url = f"http://{os.getenv('NEHS_SERVER')}:5000/sandbox"
                request_body = {"command": "get_config"}
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {os.getenv('NEHS_API_KEY')}",
                }
                response = await asyncio.to_thread(
                    requests.post, api_url, json=request_body, headers=headers
                )
                nehs_config = response.json()
                visit_motives_list = [
                    {"name": visit_motive["name"], "id": visit_motive["id"]}
                    for visit_motive in nehs_config["visit_motives"]
                ]

                visit_motive_url = (
                    f"http://{os.getenv('NEHS_SERVER')}:5000/motives/{args['visit_motive']}"
                )
                visit_motive_body = {
                    "visit_motives": visit_motives_list,
                }
                response = await asyncio.to_thread(
                    requests.post, visit_motive_url, json=visit_motive_body, headers=headers
                )

                return response.json()
            except Exception as e:
                logger.error(f"API call failed in appt_check_type: {str(e)}")
                return {"error": "api_call_failed", "message": str(e)}

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(), self._tts.say("Je consulte nos motifs de rendez-vous.")
        )

        # Check if API call failed
        if api_result.get("error") == "api_call_failed":
            await self.forward_call(
                function_name, tool_call_id, args, llm, context, result_callback
            )
            return

        data = api_result
        visit_motive_id = data.get("visit_motive_id")
        open = data.get("open")
        price = data.get("price")
        instructions = data.get("instructions")

        if open == False:
            await self.not_autorized(
                function_name, tool_call_id, args, llm, context, result_callback
            )
            return

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": (
                            "Utilise cette fonction si le type de rendez-vous est correct"
                        ),
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "appt_unique_type",
                        "description": (
                            "Utilise cette fonction si le patient souhaite un autre type de rendez-vous"
                        ),
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le patient souhaite parler au secrétariat"
                        ),
                    },
                }
            ]
        )

        if visit_motive_id != "error" and visit_motive_id is not None:
            self._appt_data["visit_motive_id"] = visit_motive_id
            self._appt_data["visit_motive_name"] = data.get("name", "LABEL")
            self._appt_data["instructions"] = instructions
            self._appt_data["price"] = price
            # self._appt_data['before'] = before
            # self._appt_data['after'] = after
            self._context.add_message(
                {
                    "role": "system",
                    "content": (
                        "Demande au patient s'il souhaite bien prendre rendez-vous pour"
                        f" {self._appt_data['visit_motive_name'].lower()}. Demande bien pour ce"
                        f" motif : {self._appt_data['visit_motive_name'].lower()} pas un autre. Si"
                        " c'est le bon type de rendez-vous utilise la fonction ask_date."
                        " Sinon si le patient souhaite un autre type de rendez-vous ou si le patient dit non utilise la fonction appt_unique_type"
                    ),
                }
            )

            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
        else:
            await self.appt_unique_type(
                function_name,
                tool_call_id,
                {"additional_info": "Je n'ai pas trouvé votre type d'examen."},
                llm,
                context,
                result_callback,
            )

    async def ask_date(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        curr_date_time = datetime.now()
        curr_date_in_text = convert_utc_to_french_text(
            datetime.strftime(curr_date_time, "%Y%m%dT%H:%M:%S.000Z")
        )

        if self._ask_date_called:
            message = (
                "D'accord, à quelle date et heure préférez-vous votre rendez-vous ?"
            )
        else:
            message = (
                "Souhaitez-vous votre rendez-vous le plus tôt possible ou à une date"
                " précise ?"
            )

        if self._ask_date_called:
            message = (
                "D'accord, à quelle date et heure préférez-vous votre rendez-vous ?"
            )
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "check_availability",
                            "description": (
                                "Utilise cette fonction pour vérifier les disponibilités à la"
                                " date et l'heure spécifiées. Si le patient souhaite le plus"
                                " tôt possible, inscris la date du lendemain."
                            ),
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "time": {
                                        "type": "string",
                                        "description": (
                                            "La date et l'heure souhaités pour le ou les"
                                            " rendez-vous"
                                        ),
                                        "format": "date-time",
                                        "example": "2024-08-31T14:55:01.123456+00:00",
                                    },
                                },
                                "required": ["time"],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "forward_call",
                            "description": (
                                "Utilise cette fonction si le patient souhaite parler au secrétariat"
                            ),
                        },
                    }
                ]
            )

            self._context.add_message(
                {
                    "role": "system",
                    "content": (
                        f"{message} Le patient peut souhaiter le plus tôt possible. Une"
                        " fois que le patient a donné une date et une heure, utilisez la"
                        " fonction check_availability pour vérifier les disponibilités."
                    ),
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
        else:
            self._ask_date_called = True

            tomorrow = datetime.now().date() + timedelta(days=1)
            # Convert date object to ISO format string with time
            tomorrow_str = tomorrow.strftime("%Y-%m-%d") + "T06:00:00+00:00"
            await self.check_availability(
                function_name,
                tool_call_id,
                {"time": tomorrow_str},
                llm,
                context,
                result_callback,
            )

    async def not_autorized(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {"name": "forward_call", "description": "Utilise cette fonction pour transférer la conversation au secrétariat."},
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "Dis : 'Je ne suis pas autorisé à prendre rendez-vous pour ce motif d'examen. Souhaitez-vous être transféré au secrétariat ?' Si le patient répond oui, utilise la fonction forward_call pour transférer la conversation au secrétariat.",
            }
        )

        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )


    async def check_availability(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._availability_checked:
            return

        self._availability_checked = True

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "utilise cette fonction si le patient dit non",
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": "utilise cette fonction si le patient dit oui",
                    },
                }
            ]
        )

        async def make_api_call():
            try:
                current_date = datetime.now()
                fifteen_days_from_now = current_date + timedelta(days=60)
                current_date_str = current_date.strftime("%Y%m%dT%H:%M:%S.000Z")
                fifteen_days_from_now_str = fifteen_days_from_now.strftime(
                    "%Y%m%dT%H:%M:%S.000Z"
                )

                # Convert input time format to required format
                input_date = datetime.fromisoformat(args["time"])
                formatted_time = input_date.strftime("%Y%m%dT%H:%M:%S.000Z")
                user_start_date = datetime.strptime(
                    formatted_time, "%Y%m%dT%H:%M:%S.000Z"
                ) - timedelta(hours=self.dst)

                api_url = f"http://{os.getenv('NEHS_SERVER')}:5000/sandbox"
                request_body = {
                    "command": "get_availabilities",
                    "start_date": user_start_date.strftime("%Y%m%dT%H:%M:%S.000Z"),
                    "stop_date": fifteen_days_from_now_str,
                    "visit_motive_id": self._appt_data["visit_motive_id"],
                    "location_ids": self.location_ids,
                    "site_ids": self.site_ids,
                }
                headers = {
                    "Authorization": f"Bearer {os.getenv('NEHS_API_KEY')}",
                    "Content-Type": "application/json",
                }
                logger.debug(f"Making Request to sandbox with request body: {request_body}")
                response = await asyncio.to_thread(
                    requests.post, api_url, json=request_body, headers=headers
                )
                return response.json()
            except Exception as e:
                logger.error(f"API call failed: {str(e)}")
                return {"error": "api_call_failed", "message": str(e)}

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(),
            self._tts.say(
                "Veuillez patienter s'il-vous-plaît, je vérifie nos disponibilités."
            ),
        )

        self._availability_checked = False

        # Move this after the API call to prevent duplicate responses
        data = api_result

        # Check if API call failed completely
        if data.get("error") == "api_call_failed":
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "forward_call",
                            "description": "Utilise cette fonction pour transférer l'appel au secrétariat",
                        },
                    }
                ]
            )
            self._context.add_message(
                {
                    "role": "system",
                    "content": (
                        "Je suis désolé, il y a un problème technique avec notre système de réservation. "
                        "Souhaitez-vous que je vous transfère au secrétariat qui pourra vous aider directement ? "
                        "Si le patient accepte, utilisez la fonction forward_call."
                    ),
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
            return

        availabilities = data.get("availabilities")
        availability_error = data.get("error")

        if (
            availability_error == None
            and availabilities != None
            and len(availabilities) > 0
        ):
            # logger.debug(f"Found availabilities: {availabilities}")

            # 10h -> 12h because the timezone is UTC
            # France winter: UTC+2
            # France summer: UTC+1
            # but utc time is always constant so 10h in UTC is 11h in France during summer and 12h during winter.
            availabilities = [
                availability
                for availability in availabilities
                if not lunch_break(availability, self.dst)
            ]
            logger.debug(f"Filtered availabilities: {availabilities}")

            self._appt_data["start_date"] = availabilities[0]["start_date"]
            self._appt_data["end_date"] = availabilities[0]["stop_date"]
            self._appt_data["location_id"] = availabilities[0].get("location_id")
            self._appt_data["site_id"] = availabilities[0].get("site_id")
            self._appt_data["practitioner_id"] = availabilities[0].get(
                "practitioner_id"
            )

            # next_availability = self._appt_data["start_date"]
            logger.debug(
                f"Most recent availability UTC: {self._appt_data['start_date']}"
            )
            local_time_from_availability = datetime.strptime(
                self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
            ) + timedelta(hours=self.dst)
            local_time_from_availability_str = datetime.strftime(
                local_time_from_availability, "%Y%m%dT%H:%M:%S.000Z"
            )
            logger.debug(
                f"Most recent availability UTC+1: {local_time_from_availability_str}"
            )
            next_availability = local_time_from_availability_str
            lisible_date = convert_utc_to_french_text(self._appt_data["start_date"])
            # Compare requested date with available date
            requested_date = datetime.fromisoformat(args["time"])
            logger.debug(f"Lisible date: {lisible_date}")

            date_message = f"Le rendez-vous le plus proche est le {lisible_date}."

            # Get tomorrow's date
            tomorrow = datetime.now().date() + timedelta(days=1)

            # Only show the sorry message if the requested date is different from available date and not tomorrow
            if (
                requested_date.date() != local_time_from_availability.date()
                and requested_date.date() != tomorrow
            ):
                date_message = (
                    "Dis : 'Je suis désolé, il n'y a pas de disponibilité ce jour-ci."
                    " Le rendez-vous le plus proche de la date que"
                    f" vous m'avez demandée est le {lisible_date}.'"
                )

            if self._new_patient:

                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "save_new_patient_info",
                                "description": (
                                    "Utilises cette fonction pour sauvgarder les détails du"
                                    " patient une fois que la date de rendez-vous est"
                                    " confirmé par le patient."
                                ),
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "check_availability",
                                "description": (
                                    "Utilise cette fonction pour vérifier les"
                                    " disponibilités à la date et l'heure spécifiées. Si le"
                                    " patient souhaite le plus tôt possible, inscris la"
                                    " date du lendemain."
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "time": {
                                            "type": "string",
                                            "description": "La date et l'heure souhaités",
                                            "format": "date-time",
                                            "example": "2024-08-31T14:55:01.123456+00:00",
                                        },
                                    },
                                    "required": ["time"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": "utilise cette fonction souhaite parler au secrétariat",
                            },
                        }
                    ]
                )

                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"""{date_message} Cela vous convient-il ?

                        Tu ne peux pas proposer d'autre rendez-vous.
                        Il n'y a pas de rendez-vous disponible plus tôt.
                        Le rendez-vous n'est pas encore confirmé à cette étape.

                        Si le patient dit oui, utilisez la fonction save_new_patient_info.

                        Si le patient dit plutôt l'après-midi utilise la fonction check_availability avec la date proposée mais l'après-midi.

                        Si le patient dit non ou nom ou plus tard, demande une nouvelle date et heure, puis utiilise la fonction check_availability.

                        Surtout garde le format de date en toute lettre.""",
                    }
                )

            else:

                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "confirm_appt",
                                "description": (
                                    "Utilises cette fonction pour sauvgarder les détails du"
                                    " patient une fois que la date de rendez-vous est"
                                    " confirmé par le patient."
                                ),
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "check_availability",
                                "description": (
                                    "Utilise cette fonction pour vérifier les"
                                    " disponibilités à la date et l'heure spécifiées. Si le"
                                    " patient souhaite le plus tôt possible, inscris la"
                                    " date du lendemain."
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "time": {
                                            "type": "string",
                                            "description": "La date et l'heure souhaités",
                                            "format": "date-time",
                                            "example": "2024-08-31T14:55:01.123456+00:00",
                                        },
                                    },
                                    "required": ["time"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": "utilise cette fonction souhaite parler au secrétariat",
                            },
                        }
                    ]
                )

                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"""{date_message} Cela vous convient-il ?

                        Tu ne peux pas proposer d'autre rendez-vous.
                        Il n'y a pas de rendez-vous disponible plus tôt.
                        Le rendez-vous n'est pas encore confirmé à cette étape.

                        Si le patient dit oui, utilisez la fonction confirm_appt.

                        Si le patient dit plutôt l'après-midi utilise la fonction check_availability avec la date proposée mais l'après-midi.

                        Si le patient dit non ou nom ou plus tard, demande une nouvelle date et heure, puis utiilise la fonction check_availability.

                        Surtout garde le format de date en toute lettre.""",
                    }
                )

            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )

        else:
            if availability_error is None:
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "check_availability",
                                "description": (
                                    "Utilise cette fonction pour vérifier les disponibilités à la"
                                    " date et l'heure spécifiées. Si le patient souhaite le plus"
                                    " tôt possible, inscris la date du lendemain."
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "time": {
                                            "type": "string",
                                            "description": (
                                                "La date et l'heure souhaités pour le ou les"
                                                " rendez-vous"
                                            ),
                                            "format": "date-time",
                                            "example": "2024-08-31T14:55:01.123456+00:00",
                                        },
                                    },
                                    "required": ["time"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite deux"
                                    " rendez-vous différents"
                                ),
                            },
                        },
                    ]
                )

                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            f"""Dit au patient qu'il n'y a pas encore de disponibilités à partir de la date souhaitée. Le patient a deux choix: soit il demande une autre date, ou il demande d'être tranféré au secrétariat. Si le patient dit qu'il veut une autre date pour un rendez-vous, demande la date souhaité et appel la fonction check_availability. Si le patient dit transfert, appel la fonction forward_call et Informe que tu vas transférer l'appel vers le secrétariat."""
                        ),
                    }
                )

                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )
            else:
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "appt_check_type",
                                "description": (
                                    "Utilise cette fonction lorsque tu as compris le type de"
                                    " rendez-vous souhaité par le patient"
                                ),
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "visit_motive": {
                                            "type": "string",
                                            "description": "Le type de rendez-vous souhaité",
                                        }
                                    },
                                    "required": ["visit_motive"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "forward_call",
                                "description": (
                                    "Utilise cette fonction si le patient souhaite deux"
                                    " rendez-vous différents"
                                ),
                            },
                        },
                    ]
                )

                self._ask_date_called = False

                self._context.add_message(
                    {
                        "role": "system",
                        "content": (
                            f"""Dit: Je suis désolé, il n'y a pas de disponibilité pour le motif souhaité, est ce que voulez prendre un rendez-vous pour un autre type d'examen ou souhaitez-vous que je transfère l'appel? Si le patient dit autre motif, demande lui pour un motif et appel la fonction appt_check_type avec le motif souhaité. Si le patient dit transfert, appel la fonction forward_call et Informe que tu vas transférer l'appel vers le secrétariat."""
                        ),
                    }
                )

                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

    async def send_confirmation_sms(self):
        webhook_url = "https://n8n-self-hosted-vocca.onrender.com/webhook/46ace88a-b96e-4900-8dc3-4e5210f69d53"
        appointment_date = datetime.strptime(
            self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
        ) + timedelta(hours=self.dst)
        formatted_date = appointment_date.strftime("%d/%m/%Y à %H:%M")
        sms_content = (
            f"Votre rendez-vous pour l'examen {self._appt_data['visit_motive_name']} est confirmé"
            f" pour le {formatted_date} au {configContent['address']}. Merci d'apporter"
            " votre ordonnance."
        )

        # Use mobile number if available, otherwise use the caller's number
        contact_number = getattr(self, '_mobile_number', None) or self._caller_phone_number
        
        sms_payload = {
            "to": contact_number,
            "content": sms_content,
            "from": "Vocca",
        }

        logger.debug(f"Sending SMS to webhook with payload: {sms_payload}")
        asyncio.create_task(
            asyncio.to_thread(requests.post, webhook_url, json=sms_payload)
        )

    async def confirm_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._appointment_confirmed:

            return

        self._appointment_confirmed = True

        # self._patient_data = {
        #    "first_name": args["first_name"],
        #    "last_name": args["last_name"],
        # }

        patient_gender = self._patient_data["biological_sex"]
        if patient_gender != "male" and patient_gender != "female":
            patient_gender = "male"

        patient_gender = patient_gender[0].upper()

        api_url = f"http://{os.getenv('NEHS_SERVER')}:5000/create_hl7_message"
        request_body = {
            "patient_firstname": self._patient_data["first_name"].upper(),
            "patient_lastname": self._patient_data["last_name"].upper(),
            "birth_date": self._patient_data["birthdate"].replace("-", ""),
            "gender": patient_gender,
            "visit_motive_id": self._appt_data["visit_motive_id"],
            "exam_label": self._appt_data["visit_motive_name"],
            "start_date": datetime.strftime(
                datetime.strptime(self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z")
                + timedelta(hours=self.dst),
                "%Y%m%dT%H:%M:%S.000Z",
            ),
            "end_date": datetime.strftime(
                datetime.strptime(self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z")
                + timedelta(hours=self.dst),
                "%Y%m%dT%H:%M:%S.000Z",
            ),
            "duration": int(
                abs(
                    (
                        datetime.strptime(
                            self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z"
                        )
                        - datetime.strptime(
                            self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
                        )
                    )
                ).total_seconds()
                / 60.0
            ),
            "site_id": str(self._appt_data["site_id"]),
            "mobile": self._caller_phone_number.replace("+33", "0"),  # type: ignore
            "location_id": str(self._appt_data["location_id"]),
            "practitioner_id": str(self._appt_data["practitioner_id"]),
        }

        # Use mobile number if available, otherwise use the caller's number
        contact_number = getattr(self, '_mobile_number', None) or self._caller_phone_number
        request_body["mobile"] = contact_number.replace("+33", "0")

        logger.debug(
            "Sending message to nehs server with body for existing patient:"
            f" {request_body}"
        )
        # Uncomment below line to make the request to the nehs server
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.getenv('NEHS_API_KEY')}",
        }
        response = await asyncio.to_thread(
            requests.post, api_url, json=request_body, headers=headers
        )
        logger.debug(f"Response code from server: {response.status_code}")
        logger.debug(f"Response body from NEHS server: {response}")

        # Parse response JSON to get appointment_id
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "appointment_id" in response_data:
                    self._appt_data["appointment_id"] = response_data["appointment_id"]
                    logger.debug(f"Stored appointment_id: {response_data['appointment_id']}")
            except Exception as e:
                logger.error(f"Failed to parse response JSON: {str(e)}")

        if response.status_code == 200:
            await self.send_confirmation_sms()

            # Add record to Airtable
            airtable_url = (
                "https://api.airtable.com/v0/appnQYRzjmPbYNDGj/tblgMxYsnkoErDln5"
            )
            headers = {
                "Authorization": f"Bearer {os.getenv('AIRTABLE_API_KEY')}",
                "Content-Type": "application/json",
            }

            # Convert UTC to local time for Airtable
            appointment_start = datetime.strptime(
                self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
            ) + timedelta(hours=self.dst)
            appointment_end = datetime.strptime(
                self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z"
            ) + timedelta(hours=self.dst)

            # Format for Airtable with timezone info
            airtable_data = {
                "records": [
                    {
                        "fields": {
                            "callID": self._callId,
                            "config": configContent["config"],
                            "Number": self._caller_phone_number,
                            "Name": (
                                f"{self._patient_data['first_name']} {self._patient_data['last_name']}"
                            ),
                            "Type": self._appt_data["visit_motive_name"],
                            "Start time": appointment_start.strftime(
                                "%Y-%m-%dT%H:%M:%S%z"
                            ),
                            "End time": appointment_end.strftime("%Y-%m-%dT%H:%M:%S%z"),
                        }
                    }
                ]
            }

            try:
                airtable_response = await asyncio.to_thread(
                    requests.post, airtable_url, json=airtable_data, headers=headers
                )
                logger.debug(f"Airtable response: {airtable_response.status_code}")
            except Exception as e:
                logger.error(f"Failed to create Airtable record: {str(e)}")

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le status code de la réponse n'est pas"
                            " 200."
                        ),
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    "Le rendez-vous a été réservé avec succès pour le nouveau patient."
                    " Informez le patient que son rendez-vous est confirmé pour le"
                    f" {self._appt_data['start_date']} au {configContent['address']}."
                    " Expliquez qu'ils recevront un SMS de confirmation. Vous devrez"
                    f" obligatoirement avoir une ordonnance pour votre rendez-vous. {self._appt_data['instructions']}"
                ),
                "content": (
                    "La requête pour réserver le rendez-vous est fait. En fonction de la"
                    " réponse envoyé par le serveur, Informez le patient les détails"
                    " suivantes. Si le serveur a répondu avec un code 200, Informez le"
                    " patient que le rendez-vous a été prise avec succès, et rappelez le"
                    " patient de fournir obligatoirement leur ordonnance et expliquez"
                    " qu'ils recevront un SMS de confirmation. Si le serveur a répondu"
                    " avec un code qui n'est pas 200, Informez le patient qu'il y avait un"
                    " problème technique et mentionnez que vous allez rédigirer l'appel"
                    " vers un responsable, et appelez la fonction forward_call. Voici le"
                    f" code reçu par le serveur: {response.status_code}"
                ),
            }
        )

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def save_new_patient_info(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "get_birth_date",
                        "description": (
                            "Utilise cette fonction une fois que tu as confirmé le nom de famille et le prénom du patient"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "first_name": {
                                    "type": "string",
                                    "description": "Le prénom du patient",
                                },
                                "last_name": {
                                    "type": "string",
                                    "description": "Le nom de famille du patient",
                                }
                            },
                            "required": ["first_name", "last_name"],
                        },
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": "utilise cette fonction souhaite parler au secrétariat",
                    },
                }
            ]
        )
        self._context.add_message(
            {
                    "role": "system",
                    "content": (
                        "Objectif :"
                        " Demander au patient son nom de famille, puis son prénom. Récupérer uniquement les lettres épelées pour construire les noms. Obtenir une confirmation une seule fois pour chaque nom. Ne jamais répéter deux fois. Passer ensuite à la date de naissance."
                        ""
                        " RÈGLE IMPORTANTE - ÉPELLATION AVEC SSML :"
                        " Quand tu épelles un nom ou prénom pour confirmation, utilise OBLIGATOIREMENT les tags SSML comme ceci :"
                        " <prosody rate=\"-15%\"><say-as interpret-as=\"characters\">MARIGNAN</say-as></prosody>"
                        " Exemple complet : \"J'ai noté Marignan, <prosody rate=\"-15%\"><say-as interpret-as=\"characters\">MARIGNAN</say-as></prosody>. C'est bien cela ?\""
                        ""
                        " Style de parole :"
                        " Professionnel, clair, chaleureux. Toujours vouvoyer. Phrases courtes. Ne jamais utiliser de tournures familières ou vagues. Ne jamais dire \"je pense que\"."
                        ""
                        " Règles de dialogue :"
                        " •  Demander explicitement d'épeler le nom lettre par lettre."
                        " •  Confirmer une seule fois avec SSML : \"J'ai noté Marignan, <say-as interpret-as=\"characters\">MARIGNAN</say-as>, c'est bien cela ?\""
                        " •  Si le patient dit non ou corrige, redemander une seule fois : \"Pouvez-vous réépeler votre nom, lettre par lettre ?\""
                        " •  Après cette deuxième tentative, *ne pas re-confirmer*. Juste dire \"Merci, cette fois-ci j'ai bien noté votre nom.\""
                        " •  Enchaîner ensuite sur la demande du prénom, même procédure avec SSML."
                        " •  Ensuite, lancer get_birth_date."
                        ""
                        " Autres regles :"
                        " Attention parfois les patients peuvent dire plusieurs choses avant de donner leur nom par exemple : \"Attendez quelques secondes je vous donne mon nom\" ou alors dire \"c'est Eliott, E L I O 2 T\" il faut que tu comprennes que quand il dit 2 puis une lettre c'est qu'il souhaite la doubler."
                        ""
                        " Tout le texte provident d'une transcription audio. Ton objectif est d'offrir une experience agreable au patient pour collecter son nom. N'indique pas ta mission et agis comme une secretaire medicale agreable."
                        ""
                        " Scénario 1 (succès immédiat) :"
                        " IA : J'ai besoin de prendre vos informations pour finaliser votre rendez-vous. Pouvez-vous m'indiquer votre nom de famille, en l'épelant lettre par lettre ?"
                        " PATIENT : Marignan, M-A-R-I-G-N-A-N"
                        " IA : Merci. J'ai noté Marignan, <prosody rate=\"-15%\"><say-as interpret-as=\"characters\">MARIGNAN</say-as></prosody>. C'est bien cela ?"
                        " PATIENT : Oui"
                        " IA : Très bien, pouvez-vous maintenant m'indiquer votre prénom, lettre par lettre ?"
                        ""
                        " Scénario 2 (correction après première tentative) :"
                        " IA : Pouvez-vous me donner votre nom de famille, en l'épelant ?"
                        " PATIENT : Dauchat, D-A-U-C-A-T"
                        " IA : Merci. J'ai noté Daucat, <prosody rate=\"-15%\"><say-as interpret-as=\"characters\">DAUCAT</say-as></prosody>. C'est bien cela ?"
                        " PATIENT : Non, c'est D-A-U-C-H-A-T"
                        " IA : Merci, cette fois-ci j'ai bien noté votre nom de famille. Pouvez-vous maintenant m'indiquer votre prénom, en l'épelant lettre par lettre ?"
                    ),
                }
            )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )



    async def get_birth_date(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        # Store patient data from the unified function
        self._temp_patient_data = {
            "first_name": args["first_name"],
            "last_name": args["last_name"]
        }

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "check_phone_number",
                        "description": (
                            "Utilise cette fonction une fois que tu as la date de naissance du"
                            " patient"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "first_name": {
                                    "type": "string",
                                    "description": "Le prénom du patient",
                                },
                                "last_name": {
                                    "type": "string",
                                    "description": "Le nom de famille du patient",
                                },
                                "birth_date": {
                                    "type": "string",
                                    "description": (
                                        "La date de naissance du patient au format YYYYMMJJ"
                                        " tout collé ensemble. Respecte bien ce format."
                                    ),
                                },
                            },
                            "required": ["first_name", "last_name", "birth_date"],
                        },
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": "utilise cette fonction souhaite parler au secrétariat",
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    "Pour finir, demandez la date de naissance du patient sous le format 29"
                    " mars 1982 sans parler du format et sans prononcer le prénom du"
                    " patient et le nom du patient. Une fois que vous avez une date de"
                    " naissance valide au bon format, utilisez la fonction"
                    " check_phone_number avec toutes les informations collectées."
                ),
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def check_phone_number(self, function_name, tool_call_id, args, llm, context, result_callback):
        self._temp_patient_data = {
            "first_name": args["first_name"],
            "last_name": args["last_name"],
            "birth_date": args["birth_date"]
        }
        
        # Check if the phone number is a French landline (starting with 01, 02, 03, 04, 05)
        is_french_landline = False
        if self._caller_phone_number and len(self._caller_phone_number) >= 3:
            # Remove the country code if present
            phone_without_prefix = self._caller_phone_number
            if phone_without_prefix.startswith("+33"):
                phone_without_prefix = "0" + phone_without_prefix[3:]
            
            # Check if the number starts with landline prefixes
            landline_prefixes = ["01", "02", "03", "04", "05", "09"]
            if any(phone_without_prefix.startswith(prefix) for prefix in landline_prefixes):
                is_french_landline = True
        
        if is_french_landline or self._caller_phone_number == "+33641905068":
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "save_mobile_number",
                    "description": "Utilise cette fonction une fois que tu as le numéro de téléphone mobile du patient",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "mobile_number": {
                                "type": "string",
                                "description": "Le numéro de téléphone mobile du patient au format international +33XXXXXXXXX (sans espaces ni tirets)",
                                "example": "+33641905068"
                            }
                        },
                        "required": ["mobile_number"]
                    },
                },
            }])
            self._context.add_message({
                "role": "system", 
                "content": "Je vois que vous appelez d'un téléphone fixe. Pour pouvoir vous envoyer une confirmation par SMS, j'aurais besoin de votre numéro de téléphone portable. Pas besoin de parler du format au patient. Une fois que vous avez le numéro mobile au format international +33, utilisez la fonction save_mobile_number."
            })
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
        else:
            # If not a landline, proceed directly to appointment confirmation
            await self.confirm_new_patient_appt(function_name, tool_call_id, args, llm, context, result_callback)
    
    async def save_mobile_number(self, function_name, tool_call_id, args, llm, context, result_callback):
        mobile_number = args["mobile_number"]
        
        # Ensure the mobile number is in the correct format
        if not mobile_number.startswith("+33"):
            # Convert from potential 0X format to +33X format
            if mobile_number.startswith("0"):
                mobile_number = "+33" + mobile_number[1:]
            else:
                # If no prefix, assume it's a French number and add +33
                mobile_number = "+33" + mobile_number
        
        # Remove any spaces or dashes
        mobile_number = mobile_number.replace(" ", "").replace("-", "")
        
        # Validate that it's a valid French mobile number (starting with +336 or +337 and has correct length)
        is_valid_mobile = (mobile_number.startswith("+336") or mobile_number.startswith("+337")) and len(mobile_number) == 12
        
        if not is_valid_mobile:
            # If validation fails, ask again for a valid mobile number
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "save_mobile_number",
                    "description": "Utilise cette fonction une fois que tu as le numéro de téléphone mobile du patient",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "mobile_number": {
                                "type": "string",
                                "description": "Le numéro de téléphone mobile du patient au format international +33XXXXXXXXX (sans espaces ni tirets)",
                                "example": "+33641905068"
                            }
                        },
                        "required": ["mobile_number"]
                    },
                },
            }])
            self._context.add_message({
                "role": "system", 
                "content": "Pourriez-vous me redire votre numéro de portable s'il vous plaît ? Pas besoin de parler du format au patient. Une fois que vous avez le numéro mobile expliqué clairement, utilisez la fonction save_mobile_number."
            })
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return
        
        # Store the valid mobile number for the appointment
        self._mobile_number = mobile_number
        
        # Proceed with the appointment confirmation using the stored patient data
        await self.confirm_new_patient_appt(
            function_name, 
            tool_call_id, 
            {
                "first_name": self._temp_patient_data["first_name"],
                "last_name": self._temp_patient_data["last_name"],
                "birth_date": self._temp_patient_data["birth_date"]
            }, 
            llm, 
            context, 
            result_callback
        )

    async def confirm_new_patient_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._appointment_confirmed:
            return

        self._appointment_confirmed = True

        gender = await infer_gender_from_name(args["first_name"])

        api_url = f"http://{os.getenv('NEHS_SERVER')}:5000/create_hl7_message"
        request_body = {
            "patient_firstname": args["first_name"].upper(),
            "patient_lastname": args["last_name"].upper(),
            "birth_date": args["birth_date"],
            "gender": gender,
            "visit_motive_id": self._appt_data["visit_motive_id"],
            "exam_label": self._appt_data["visit_motive_name"],
            "start_date": datetime.strftime(
                datetime.strptime(self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z")
                + timedelta(hours=self.dst),
                "%Y%m%dT%H:%M:%S.000Z",
            ),
            "end_date": datetime.strftime(
                datetime.strptime(self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z")
                + timedelta(hours=self.dst),
                "%Y%m%dT%H:%M:%S.000Z",
            ),
            "duration": int(
                abs(
                    (
                        datetime.strptime(
                            self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z"
                        )
                        - datetime.strptime(
                            self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
                        )
                    )
                ).total_seconds()
                / 60.0
            ),
            "site_id": str(self._appt_data["site_id"]),
            "mobile": self._caller_phone_number.replace("+33", "0"),  # type: ignore
            "location_id": str(self._appt_data["location_id"]),
            "practitioner_id": str(self._appt_data["practitioner_id"]),
        }

        # Use mobile number if available, otherwise use the caller's number
        contact_number = getattr(self, '_mobile_number', None) or self._caller_phone_number
        request_body["mobile"] = contact_number.replace("+33", "0")

        logger.debug(
            "Sending message to nehs server with body for a new patient:"
            f" {request_body}"
        )
        # Uncomment below line to make the request to the nehs server
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.getenv('NEHS_API_KEY')}",
        }
        response = await asyncio.to_thread(
            requests.post, api_url, json=request_body, headers=headers
        )
        logger.debug(f"Response code from server: {response.status_code}")
        logger.debug(f"Response body from NEHS server: {response}")

        # Parse response JSON to get appointment_id
        if response.status_code == 200:
            try:
                response_data = response.json()
                if "appointment_id" in response_data:
                    self._appt_data["appointment_id"] = response_data["appointment_id"]
                    logger.debug(f"Stored appointment_id: {response_data['appointment_id']}")
            except Exception as e:
                logger.error(f"Failed to parse response JSON: {str(e)}")

        if response.status_code == 200:
            await self.send_confirmation_sms()

            # Store the NEHS request data
            self._request_data = request_body

            # Add record to Airtable
            airtable_url = (
                "https://api.airtable.com/v0/appnQYRzjmPbYNDGj/tblgMxYsnkoErDln5"
            )
            headers = {
                "Authorization": f"Bearer {os.getenv('AIRTABLE_API_KEY')}",
                "Content-Type": "application/json",
            }

            # Convert UTC to local time for Airtable
            appointment_start = datetime.strptime(
                self._appt_data["start_date"], "%Y%m%dT%H:%M:%S.000Z"
            ) + timedelta(hours=self.dst)
            appointment_end = datetime.strptime(
                self._appt_data["end_date"], "%Y%m%dT%H:%M:%S.000Z"
            ) + timedelta(hours=self.dst)

            # Format for Airtable with timezone info
            airtable_data = {
                "records": [
                    {
                        "fields": {
                            "callID": self._callId,
                            "config": configContent["config"],
                            "Number": self._caller_phone_number,
                            "Name": f"{args['first_name']} {args['last_name']}",
                            "Type": self._appt_data["visit_motive_name"],
                            "Start time": appointment_start.strftime(
                                "%Y-%m-%dT%H:%M:%S%z"
                            ),
                            "End time": appointment_end.strftime("%Y-%m-%dT%H:%M:%S%z"),
                        }
                    }
                ]
            }

            try:
                airtable_response = await asyncio.to_thread(
                    requests.post, airtable_url, json=airtable_data, headers=headers
                )
                logger.debug(f"Airtable response: {airtable_response.status_code}")
            except Exception as e:
                logger.error(f"Failed to create Airtable record: {str(e)}")

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": (
                            "Utilise cette fonction si le status code de la réponse n'est pas"
                            " 200."
                        ),
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": (
                    "Le rendez-vous a été réservé avec succès pour le nouveau patient."
                    " Informez le patient que son rendez-vous est confirmé pour le"
                    f" {self._appt_data['start_date']} au {configContent['address']}."
                    " Expliquez qu'ils recevront un SMS de confirmation. Vous devrez"
                    f" obligatoirement avoir une ordonnance pour votre rendez-vous. {self._appt_data['instructions']}"
                ),
                "content": (
                    "La requête pour réserver le rendez-vous est fait. En fonction de la"
                    " réponse envoyé par le serveur, Informez le patient les détails"
                    " suivantes. Si le serveur a répondu avec un code 200, Informez le"
                    " patient que le rendez-vous a été prise avec succès, et rappelez le"
                    " patient de fournir obligatoirement leur ordonnance et expliquez"
                    " qu'ils recevront un SMS de confirmation. Si le serveur a répondu"
                    " avec un code qui n'est pas 200, Informez le patient qu'il y avait un"
                    " problème technique et mentionnez que vous allez rédigirer l'appel"
                    " vers un responsable, et appelez la fonction forward_call. Voici le"
                    f" code reçu par le serveur: {response.status_code}"
                ),
            }
        )

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )


from base_main import base_main


async def main(
    room_url: str,
    token: str,
    callId: str,
    sipUri: str,
    config_id: str,
    caller_phone: str,
):

    global_prompt = f"""
        1. IDENTITÉ ET CONTEXTE
        ---
        Nous sommes le {{current_date}}.

        Tu es Marie, assistante virtuelle chaleureuse et professionnelle à {configContent['name']}.
        Ton rôle se situe au sein d'un centre de radiologie à Albi.
        Les patients t'appellent généralement pour de la prise de rendez-vous ou pour poser des questions liées à leurs examens.
        Ne dis bonjour qu'une seule fois.

        2. RÔLE
        ---
        Tu ne peux traiter que les appels pour des rendez-vous de radiographie. Pour les autres demandes, tu proposeras de transférer au secrétariat.
        Tu ne peux pas non plus gérer les annulations ou modifications de rendez-vous : tu proposeras de transférer ces demandes au secrétariat.

        Tu échanges directement avec les patients, en leur offrant une expérience simple, polie et bienveillante.
        Tu as accès à l'emploi du temps des salles de radiologie et aux informations de base du centre.

        Tu ne peux pas choisir un médecin en particulier pour un examen. Si un patient insiste pour choisir son médecin, propose de transférer au secrétariat, qui pourra l'assister.

        - Répondre, dans une certaine mesure, aux questions simples (ex. horaires, adresse).
        - Transférer (ou prendre une note pour un rappel) si le motif sort de ton champ d'action.

        3. STYLE DE CONVERSATION
        ---
        - Utilise des phrases courtes et simples.
        - Maintiens un ton poli, courtois, professionnel et rassurant.
        - Ne dis « bonjour » qu'une seule fois pour éviter les répétitions.
        - Ne donne aucun conseil médical. Si on t'en demande, rappelle que tu n'es pas autorisée à le faire.
        - Parfois, reformule très brièvement ce que la personne vient de dire pour montrer que tu l'écoutes, sans le faire de manière systématique.

        4. DIRECTIVES PRINCIPALES
        ---
        - Tu connais déjà le numéro de téléphone de l'appelant : pas besoin de le redemander. Si le patient veut te le donner, précise simplement que tu l'as déjà.
        - Tu es le secrétariat virtuel du centre. S'il y a un besoin important, propose de transférer au secrétariat.
        - Si la conversation est confuse (due à la retranscription), fais de ton mieux pour clarifier en posant des questions, et reste concis.
        - Ne fournis aucune information personnelle ou confidentielle autre que le strict nécessaire.

        5. RÈGLES SPÉCIALES - ÉPELLATION AVEC SSML
        ---
        IMPORTANT : Quand tu épelles des noms ou prénoms pour confirmation, utilise OBLIGATOIREMENT les tags SSML :
        - Format à utiliser : <prosody rate="-15%"><say-as interpret-as="characters">TEXTE</say-as></prosody>
        - Exemple : "J'ai noté Martin, <prosody rate="-15%"><say-as interpret-as="characters">MARTIN</say-as></prosody>. C'est bien cela ?"
        - Utilise ces tags uniquement pour l'épellation de noms et prénoms lors de confirmations.

        6. RÈGLES DE TRANSFERT OU DE PRISE DE NOTE
        ---
        - Si le patient souhaite absolument parler à un humain (médecin ou secrétariat), propose de le transférer au secrétariat.
        - Si la demande est hors de tes compétences (IRM, scanner, urgence vitale…), propose de transférer au secrétariat.
        - Si le patient veut choisir un médecin en particulier pour son rendez-vous, propose de le transférer au secrétariat.

        Pour proposer un transfert, dis par exemple :
        « Je ne peux pas donner suite à votre demande concernant la demande du patient. Souhaitez-vous que je vous transfère au secrétariat ? »

        - Si le patient répond oui, utilise la fonction forward_call.
        - S'il refuse ou si tu ne comprends pas, poursuis : « Pas de souci. Dites-moi comment je peux vous aider. »

        7. INFORMATIONS UTILES
        ---
        - Adresse du centre : {configContent['address']}
        - Email du secrétariat (à prononcer ainsi) : {convert_email_to_spoken_text(configContent['email'])}
        - Horaires d'ouverture : {configContent['openings']} {configContent['additional_information']}

        8. NOTES IMPORTANTES
        ---
        - Ton rôle est de simplifier la vie du patient et du centre.
        - Aucun conseil médical : tu es une assistante administrative.
        - Il t'est interdit de répondre à des questions médicales, même sur demande.
        - Le numéro de téléphone du patient est déjà capté.
        - Reste professionnelle, évite tout superflu et ne laisse pas de longs silences inutiles.
        """

    try:
        await base_main(
            room_url,
            token,
            callId,
            sipUri,
            AlbiIntakeProcessor,
            config_id,
            global_prompt,
            caller_phone,
            configContent["search_url"],
        )
    except Exception as e:
        logger.error(f"Error in Deltour main: {str(e)}")
        raise


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Opticim Flow")
    parser.add_argument("-u", type=str, help="Room URL")
    parser.add_argument("-t", type=str, help="Token")
    parser.add_argument("-i", type=str, help="Call ID")
    parser.add_argument("-s", type=str, help="SIP URI")
    parser.add_argument("-c", type=str, help="Config ID")
    parser.add_argument("-p", type=str, help="Caller phone number")
    config = parser.parse_args()

    if supabase is None:
        raise ValueError("Supabase client is not initialized")

    response = (
        supabase.table("knowledge-bases").select("*").eq("config", config.c).execute()
    )
    if len(response.data) == 0:
        raise ValueError(f"No config found for id {config.c}")

    configContent = response.data[0]

    asyncio.run(main(config.u, config.t, config.i, config.s, config.c, config.p))
