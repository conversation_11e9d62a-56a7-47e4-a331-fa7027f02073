import aiohttp
import os
from datetime import datetime

import random

from aiohttp.client_exceptions import ContentTypeError
from twilio.base.exceptions import TwilioRestException

from pipecat.transports.services.daily import DailyTransport, DailyTranscriptionSettings, DailyParams
from pipecat.services.azure import AzureSTTService

from pipecat.processors.aggregators.llm_response import (
    LLMUserContextAggregator,
    LLMAssistantContextAggregator,
)

from pipecat.processors.audio.vad.silero import <PERSON><PERSON>o<PERSON><PERSON>nalyzer
from pipecat.audio.vad.vad_analyzer import VADParams
from pipecat.processors.filters.stt_mute_filter import STTMuteConfig, STTMuteFilter, STTMuteStrategy

from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import <PERSON><PERSON>ineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.frames.frames import EndFrame, LLMMessagesFrame, LLMUpdateSettingsFrame

from pipecat.services.openai import OpenAILLMContext, OpenAILLMContextFrame, OpenAILLMService, NOT_GIVEN
from pipecat.services.azure import AzureLLMService

from pipecat.processors.user_idle_processor import UserIdleProcessor

from newCartesia import CartesiaTTSService
from pipecat.services.azure import AzureTTSService

from pipecat.transcriptions.language import Language

from loguru import logger

from datetime import datetime
import pytz

from utils import generate_transcript, send_to_webhook, fetch_recording_url

from twilio.rest import Client

from new_soundfile_mixer import SoundfileMixer

import asyncio

import sys

async def initialize_azure_tts(config):
    import ssl
    
    # Create SSL context with default settings
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    azure_speech_keys = [
        os.getenv("AZURE_SPEECH_KEY_1", ""),
        os.getenv("AZURE_SPEECH_KEY_2", ""),
        os.getenv("AZURE_SPEECH_KEY_3", ""),
        os.getenv("AZURE_SPEECH_KEY_4", ""),
        os.getenv("AZURE_SPEECH_KEY_5", ""),
        os.getenv("AZURE_SPEECH_KEY_6", ""),
        os.getenv("AZURE_SPEECH_KEY_7", ""),
        os.getenv("AZURE_SPEECH_KEY_8", ""),
        os.getenv("AZURE_SPEECH_KEY_9", ""),
        os.getenv("AZURE_SPEECH_KEY_10", ""),
        os.getenv("AZURE_SPEECH_KEY_11", "")
    ]
    selected_azure_key = random.choice(azure_speech_keys)

    # Randomly select between the two Cartesia API keys
    cartesia_api_key = random.choice([
        os.getenv("CARTESIA_API_KEY"),
        os.getenv("CARTESIA_API_KEY_2")
    ])
    
    # return CartesiaTTSService(
    #     api_key=cartesia_api_key,
    #     voice_id=os.getenv("CARTESIA_VOICE_ID"),
    #     params=CartesiaTTSService.InputParams(
    #         language=Language.FR,
    #         emotion=["positivity:high"]
    #     ),
    #     model="sonic",
    #     ssl_context=ssl_context,
    #     sample_rate=16000
    # )
    return AzureTTSService(
        api_key=selected_azure_key,
        region=os.getenv("AZURE_REGION"),
        voice="fr-FR-DeniseNeural",
        params=AzureTTSService.InputParams(
            language=Language.FR,
            rate="1.05"))

async def initialize_llm():
    return AzureLLMService(
        api_key=os.getenv("AZURE_OPENAI_KEY"),
        endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"), 
        model="gpt-4o-mini",
        params=AzureLLMService.InputParams(
            temperature=0,
            extra={"parallel_tool_calls": False}
        )
    )

async def initialize_twilio():
    return Client(os.getenv("TWILIO_ACCOUNT_SID"), os.getenv("TWILIO_AUTH_TOKEN"))


async def initialize_azure_stt():
    # Utilise la même clé Azure que pour TTS ou une clé dédiée
    azure_speech_keys = [
        os.getenv("AZURE_SPEECH_KEY_1", ""),
        os.getenv("AZURE_SPEECH_KEY_2", ""),
        os.getenv("AZURE_SPEECH_KEY_3", ""),
        os.getenv("AZURE_SPEECH_KEY_4", ""),
        os.getenv("AZURE_SPEECH_KEY_5", ""),
        os.getenv("AZURE_SPEECH_KEY_6", ""),
        os.getenv("AZURE_SPEECH_KEY_7", ""),
        os.getenv("AZURE_SPEECH_KEY_8", ""),
        os.getenv("AZURE_SPEECH_KEY_9", ""),
        os.getenv("AZURE_SPEECH_KEY_10", ""),
        os.getenv("AZURE_SPEECH_KEY_11", "")
    ]
    selected_azure_key = random.choice(azure_speech_keys)
    
    return AzureSTTService(
        api_key=selected_azure_key,
        region=os.getenv("AZURE_REGION"),
        language="fr-FR",
        # language=Language.FR,
        # sample_rate=16000
    )


async def initialize_services(config):
    tts, llm, twilio_client, stt = await asyncio.gather(
        initialize_azure_tts(config), initialize_llm(), initialize_twilio(), initialize_azure_stt()
    )
    return tts, llm, twilio_client, stt


async def base_main(
    room_url: str,
    token: str,
    callId: str,
    sipUri: str,
    IntakeProcessor,
    config,
    global_prompt,
    caller_phone: str,
    search_url: str,
):
    try:

        async with aiohttp.ClientSession() as session:
            # Run API call and service initialization concurrently
            patient_search, (tts, llm, twilioclient, stt) = await asyncio.gather(
                session.get(
                    "https://doctolib-secret-scrape-global.onrender.com/api/v2/users/search-patient",
                    json={"string": caller_phone, "config": config},
                    headers={
                        "Authorization": f"Bearer {os.getenv('SECRET_DOCTOLIB_TOKEN')}",
                        "Content-Type": "application/json",
                    },
                ),
                initialize_services(config),
            )

            result = await patient_search.json()
            next_appt = ""

            # Check if result is a list
            if isinstance(result, list):
                patient_list = result
            # Check if result is a dictionary with a "data" key
            elif isinstance(result, dict) and "data" in result:
                patient_list = result["data"]
            else:
                # Handle unexpected format
                patient_list = []
                logger.warning(f"Unexpected patient search result format: {type(result)}")
            
            if len(patient_list) == 1:
                patient_data = patient_list[0]

                if patient_data["next_appointment"] is not None:
                    patient_data["appt_id"] = patient_data["next_appointment"]["id"]
                    patient_data["agenda_id"] = patient_data["next_appointment"][
                        "agenda_id"
                    ]
                    patient_data["medecin"] = patient_data["next_appointment"][
                        "medecin"
                    ]
                    patient_data["visit_motive_name"] = patient_data[
                        "next_appointment"
                    ]["visit_motive_name"]
                    patient_data["visit_motive_id"] = patient_data[
                        "last_visit_motive_id"
                    ]
                    patient_data["appt_date"] = patient_data["next_appointment"][
                        "start_date"
                    ]

                    # Fetch detailed appointment information
                    appt_url = f"https://doctolib-secret-scrape-global.onrender.com/api/v1/appt/{patient_data['appt_id']}"
                    appt_details_response = await session.get(
                        appt_url,
                        json={"config": config},
                        headers={
                            "Authorization": f"Bearer {os.getenv('SECRET_DOCTOLIB')}",
                            "Content-Type": "application/json",
                        },
                    )
                    appt_details = await appt_details_response.json()
                    patient_data["appointment_details"] = appt_details

                    print(appt_details["set_appointment_ids"])

                    # Update this line to make datetime.now() timezone-aware
                    current_time = datetime.now(pytz.utc)  # Make current time aware

                    # Change the comparison to use the aware current_time
                    if (
                        datetime.fromisoformat(patient_data["appt_date"])
                        >= current_time
                    ):
                        next_appt = f"Si le patient demande une confirmation ou la date de son prochain rendez-vous avant les changements est : {patient_data['visit_motive_name']} à cette date : {patient_data['appt_date']}. Ce rendez-vous est bien confirmé."
            else:
                patient_data = {}

        script_dir = os.path.dirname(os.path.abspath(__file__))
        soundfile_mixer = SoundfileMixer(
            sound_files={
                "office": os.path.join(script_dir, "assets", "new_office.mp3")
            },
            default_sound="office",
            volume=0.2,
        )

        current_date = datetime.now().strftime("%d %B %Y")
        messages = [
            {
                "role": "system",
                "content": global_prompt.format(current_date=current_date) + next_appt,
            }
        ]
        context = OpenAILLMContext(messages=messages)
        user_context = LLMUserContextAggregator(context)
        assistant_context = LLMAssistantContextAggregator(context)

        intake = IntakeProcessor(
            context, llm, tts, callId, config, patient_data=patient_data
        )

        for func_name in [
            "forward_call",
            "check_intent",
            "set_appt",
            "appt_intent",
            "appt_confirm",
            "set_question",
            "recap_question",
            "appt_check_type",
            "ask_date",
            "check_availability",
            "confirm_appt",
            "cancel_appt",
            "save_new_patient_info",
            "confirm_new_patient_appt",
            "appt_type",
            "child_check",
            "urgent",
            "route_center",
            "retard",
            "no_appt",
            "appt_double_type",
            "appt_double_type_1",
            "appt_unique_type",
            "appt_check_double_type",
            "get_last_name",
            "get_birth_date",
            "general_type",
            "end_call",
            "special_appt",
            "scanner",
            "infiltration",
            "irm",
            "hysterosalpingographie"
        ]:
            try:
                llm.register_function(func_name, getattr(intake, func_name))
            except AttributeError:
                continue

        # Keywords no longer needed since we're using Azure STT instead of Daily transcription

        vad_params = VADParams(
            confidence=0.2,
            start_secs=0.2,  # Default is 0.2 seconds
            stop_secs=1,   # Default is 0.8 seconds
            min_volume=0.2
        )

        #vad = SileroVAD(vad_params=vad_params)

        daily_api_key = os.getenv("DAILY_API_KEY")
        transport = DailyTransport(
            room_url,
            token,
            "Chatbot",
            DailyParams(
                api_key=daily_api_key,
                dialin_settings=None,
                audio_in_enabled=True,
                audio_out_enabled=True,
                audio_out_mixer=soundfile_mixer,
                camera_out_enabled=False,
                vad_enabled=True,
                vad_analyzer=SileroVADAnalyzer(params=vad_params),
                vad_audio_passthrough=True,
                transcription_enabled=False,  # Désactivé car nous utilisons Azure STT
            ),
            # user_context_processor=user_context,
            # llm=llm,
            # intake_processor=intake,
        )

        async def user_idle_callback(user_idle: UserIdleProcessor):
            # Only trigger if we've received at least one user message
            if len([m for m in messages if m["role"] == "user"]) > 0:
                messages.append(
                    {
                        "role": "system",
                        "content": "Relance la conversation avec le patient tout en expliquant que tu as mal entendu.",
                    }
                )
                await user_idle.push_frame(
                    LLMUpdateSettingsFrame(
                        settings={"extra": {"parallel_tool_calls": NOT_GIVEN}}
                    )
                )
                await user_idle.push_frame(LLMMessagesFrame(messages))
                await user_idle.push_frame(
                    LLMUpdateSettingsFrame(
                        settings={"extra": {"parallel_tool_calls": False}}
                    )
                )
                
        user_idle = UserIdleProcessor(callback=user_idle_callback, timeout=15.0)

        # Configure STTMuteFilter
        stt_mute_processor = STTMuteFilter(
            config=STTMuteConfig(
                strategies={
                    STTMuteStrategy.ALWAYS
                }
            ),
        )

        pipeline = Pipeline(
            [
                transport.input(),
                stt_mute_processor,
                stt,
                user_idle,
                user_context,
                llm,
                tts,
                transport.output(),
                assistant_context,
            ]
        )

        task = PipelineTask(pipeline, params=PipelineParams(allow_interruptions=False))

        @transport.event_handler("on_first_participant_joined")
        async def on_first_participant_joined(transport, participant):
            await transport.capture_participant_transcription(participant["id"])
            logger.info(f"Context is: {context}")
            await task.queue_frames([OpenAILLMContextFrame(context)])

        @transport.event_handler("on_participant_left")
        async def on_participant_left(transport, participant, reason):
            try:
                logger.info(f"Participant left: {participant['id']}")
                logger.info(f"Reason for leaving: {reason}")

                call_duration = datetime.now() - intake._call_start_time
                logger.info(f"Call duration: {call_duration}")

                transcript = generate_transcript(context.messages)
                logger.info(f"Conversation transcript:\n{transcript}")

                recording_url = await fetch_recording_url(twilioclient, intake._callId)

                call_data = {
                    "twilio_call_id": intake._callId,
                    "config": intake._config,
                    "recording_url": recording_url,
                    "patient_phone_number": intake._caller_phone_number,
                    "participant_id": participant["id"],
                    "reason_for_leaving": reason,
                    "call_duration": str(call_duration),
                    "patient_data": intake._patient_data,
                    "appointment_data": intake._appt_data,
                    "conversation_transcript": transcript,
                    "call_forwarded": intake._call_forwarded,
                    "new_patient": intake._new_patient,
                    "request_data": intake._request_data,
                }

                for key, value in call_data.items():
                    logger.info(f"{key}: {value}")

                supa_webhook = "https://n8n-self-hosted-vocca.onrender.com/webhook/d6659d2b-38e1-4050-bc8a-7f388d4d408d"

                await send_to_webhook(supa_webhook, call_data)

                await task.queue_frame(EndFrame())

                logger.info("Call session completed and data sent to webhook")

                sys.exit(0)

            except Exception as e:
                logger.error(f"Error in on_participant_left: {str(e)}")

                sys.exit(1)

        @transport.event_handler("on_call_state_updated")
        async def on_call_state_updated(transport, state):
            if state == "left":
                await task.queue_frame(EndFrame())

        @transport.event_handler("on_dialin_ready")
        async def on_dialin_ready(transport, cdata):
            logger.info(f"Attempting to forward call: {callId} {sipUri}")
            await asyncio.sleep(3)
            try:
                # Special handling for specific phone numbers
                if (
                    caller_phone in ["+18553308653", "0018553308653"]
                    and config == "config28"
                ):
                    forward_number = "+33188832223"
                    updated_call = twilioclient.calls(callId).update(
                        twiml=f'<Response><Dial record="record-from-answer">{forward_number}</Dial></Response>'
                    )
                elif (
                    caller_phone in ["+18553308653", "0018553308653"]
                    and config == "config27"
                ):
                    forward_number = "+33185016803"
                    updated_call = twilioclient.calls(callId).update(
                        twiml=f'<Response><Dial record="record-from-answer">{forward_number}</Dial></Response>'
                    )
                else:
                    # Original SIP forwarding logic
                    updated_call = twilioclient.calls(callId).update(
                        twiml=f'<Response><Dial record="record-from-answer"><Sip>{sipUri}</Sip></Dial></Response>'
                    )
                logger.info(f"Updated call status: {updated_call.status}")
            except TwilioRestException as e:
                logger.error(f"Failed to forward call: {str(e)}")
                logger.error(f"CallId: {callId}")
                logger.error(f"SipUri: {sipUri}")
                await task.queue_frame(EndFrame())
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                await task.queue_frame(EndFrame())

        runner = PipelineRunner()
        await runner.run(task)

    except ContentTypeError as e:
        logger.error(e)

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise
