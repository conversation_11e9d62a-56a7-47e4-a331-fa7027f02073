import asyncio
import time
import traceback
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import PlainTextResponse
from loguru import logger
from twilio.twiml.voice_response import VoiceResponse

from lib.supabase_client import supabase_client
from lib.n8n_client import n8n_client
from lib.daily_client import DailyClient
from audios import (
    DEFAULT_AUDIO_WELCOME_VOCCA,
    AUDIO_WELCOME_VOCCA_CLOSING_TIMES,
)
from constants import MAX_SESSION_TIME
import utils.time

router = APIRouter(prefix="/daily")


def get_config():
    # Import this function or make it accessible
    from main import get_config

    return get_config()


@router.post("/{customer}/{config_id}", response_class=PlainTextResponse)
async def main_start_bot(request: Request, customer: str, config_id: str):
    """
    Twilio webhook to create a Daily room and start the bot
    """
    config = get_config()
    if (
        "client_name" in request.app.state.__dict__["_state"]
        and "config_id" in request.app.state.__dict__["_state"]
    ):
        customer = request.app.state.client_name
        config_id = request.app.state.config_id

    daily_client: DailyClient = request.app.state.daily_client

    if not daily_client:
        raise HTTPException(status_code=500, detail="DailyClient not initialized")

    data = {}
    try:
        form_data = await request.form()
        data = dict(form_data)
    except Exception:
        pass

    callId = data.get("CallSid")
    caller_phone_number = data.get("From")

    # Check for anonymous call
    if caller_phone_number is not None and caller_phone_number.lower() in [
        "anonymous",
        "+33974368037",
    ]:
        resp = VoiceResponse()
        resp.say(
            language="fr-FR",
            message="Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
            rate="1.15",
        )
        resp.hangup()
        return str(resp)

    if not callId:
        raise HTTPException(status_code=500, detail="Missing 'CallSid' in request")

    exp = time.time() + (int(config.exp) if config.exp else MAX_SESSION_TIME)
    try:
        room = await daily_client.create_daily_room(exp)
        token = await daily_client.get_token(room)
        resp = VoiceResponse()

        print("Put Twilio on hold...")

        # make the caller wait for the room to be created
        company_data = supabase_client.get_company_data_by_config_id(config_id)
        welcome_audio = company_data.audio_intro_url or DEFAULT_AUDIO_WELCOME_VOCCA

        if not utils.time.is_company_open(company_data.openings_2):
            welcome_audio = AUDIO_WELCOME_VOCCA_CLOSING_TIMES

        resp.play(url=welcome_audio, loop=10)

        async def delayed_launch():
            time_to_sleep = 2 if welcome_audio == DEFAULT_AUDIO_WELCOME_VOCCA else 13
            await asyncio.sleep(time_to_sleep)
            asyncio.create_task(
                daily_client.start_customer_bot(
                    customer, room, callId, None, config_id, caller_phone_number, token
                )
            )

        asyncio.create_task(delayed_launch())

        return str(resp)
    except Exception as e:
        n8n_client.send_error_server(
            config_id,
            traceback=traceback.format_exc(),
        )
        resp = VoiceResponse()
        logger.error(f"Failed to start bot: {e}")
        resp.say(
            language="fr-FR",
            message="Bonjour, Le centre est actuellement indisponible. Merci de réessayer dans quelqus minutes. ",
            rate="1",
        )
        return str(resp)


@router.get("/dev_room")
async def dev_room(request: Request):
    """
    Create a Daily room for development purposes
    """
    daily_client: DailyClient = request.app.state.daily_client

    if not daily_client:
        raise HTTPException(status_code=500, detail="DailyClient not initialized")

    try:
        room = await daily_client.get_dev_daily_room()
        token = await daily_client.get_token(room)
        return {
            "url": room.url,
            "token": token,
            "message": "Dev room created successfully",
        }
    except Exception as e:
        logger.error(f"Failed to create dev room: {e}")
        raise HTTPException(status_code=500, detail=str(e))
