import time
from fastapi import <PERSON><PERSON>out<PERSON>, Request, HTTPException
from loguru import logger
from constants import MAX_SESSION_TIME

router = APIRouter(prefix="/status")


@router.get("")
async def get_status(request: Request):
    """
    Get the status of the server
    """
    time_now = time.time()
    # remove all clients that have been running for more than MAX_SESSION_TIME
    for call_id, start_time in list(request.app.state.running_clients.items()):
        if time_now - start_time > MAX_SESSION_TIME:
            del request.app.state.running_clients[call_id]
            logger.info(f"Removed client {call_id} from running clients")

    clients = {}
    for call_id, start_time in request.app.state.running_clients.items():
        clients[call_id] = time.strftime(
            "%Y-%m-%d %H:%M:%S", time.localtime(start_time)
        )

    return {
        "status": "running",
        "running_clients": len(request.app.state.running_clients.keys()),
        "clients": clients,
    }


@router.post("/decrement_running_clients")
async def decrement_running_clients(request: Request):
    """
    Decrement the number of running clients
    """
    data = await request.json()
    call_id = data.get("call_id")
    if call_id is None:
        raise HTTPException(status_code=400, detail="Missing 'call_id' in request")

    if call_id in request.app.state.running_clients.keys():
        del request.app.state.running_clients[call_id]

    logger.info(
        f"Number of running clients: {len(request.app.state.running_clients.keys())}"
    )
    return {"status": "success"}


@router.post("/increment_running_clients")
async def increment_running_clients(request: Request):
    """
    Increment the number of running clients
    """
    data = await request.json()
    call_id = data.get("call_id")
    if call_id is None:
        raise HTTPException(status_code=400, detail="Missing 'call_id' in request")

    start_time = time.time()
    if call_id not in request.app.state.running_clients.keys():
        request.app.state.running_clients[call_id] = start_time

    logger.info(
        f"Number of running clients: {len(request.app.state.running_clients.keys())}"
    )
    return {"status": "success"}
