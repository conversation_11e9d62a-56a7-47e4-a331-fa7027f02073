from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from loguru import logger
from scripts.create_new_bot import (
    create_customer_folder,
    replace_bot_with_customer,
    validate_customer_name,
)
import os
from github import Github
from git import Repo
from constants import GITHUB_TOKEN_PR
import datetime

# Config values (replace with env vars or settings as needed)
GITHUB_REPO = "vocca-ai/daily-full-stack"  # format: username/repo
BASE_BRANCH = "david/dev"

router = APIRouter(prefix="/generate")
dir_path = os.path.dirname(os.path.abspath(__file__))
repo_root = os.path.abspath(os.path.join(dir_path, "..", ".."))
dir_customers = os.path.join(repo_root, "src", "customers")


@router.post("/bot")
async def generate(request: Request):
    """
    Create a new bot for the given customer and open a PR
    """
    data = await request.json()
    customer = data.get("customer", "").strip()
    if not customer:
        logger.error("Customer name is required")
        return JSONResponse(
            content={"message": "Customer name is required"}, status_code=400
        )

    logger.info(f"Generating bot for customer: {customer}")
    is_valid, message = validate_customer_name(customer)
    if not is_valid:
        logger.error(f"Invalid customer name: {customer}")
        return JSONResponse(content={"message": message}, status_code=400)

    customer_path = os.path.join(dir_customers, customer)
    if os.path.exists(customer_path):
        return JSONResponse(
            content={"message": f"Customer {customer} already exists"}, status_code=400
        )

    # 1. Generate customer folder and files
    create_customer_folder(customer)
    replace_bot_with_customer(customer)

    # 2. Git: Create branch, add, commit, push
    branch_name = f"add-{customer}-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    repo = Repo(repo_root)
    origin = repo.remote(name="origin")

    # Create new branch
    repo.git.checkout(BASE_BRANCH)
    repo.git.pull()
    repo.git.checkout("-b", branch_name)

    # Add customer folder
    repo.git.add(os.path.relpath(customer_path, repo_root))
    repo.index.commit(f"Add new customer bot: {customer}")
    origin.push(branch_name)

    # 3. GitHub: Create Pull Request
    gh = Github(GITHUB_TOKEN_PR)
    github_repo = gh.get_repo(GITHUB_REPO)
    pr = github_repo.create_pull(
        title=f"Add bot for customer: {customer}",
        body=f"This PR adds a new bot setup for `{customer}`.",
        head=branch_name,
        base=BASE_BRANCH,
    )

    logger.info(f"PR created: {pr.html_url}")
    return {
        "message": f"Customer {customer} created successfully",
        "pull_request_url": pr.html_url,
    }
