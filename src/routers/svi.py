from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import PlainTextResponse
from twilio.twiml.voice_response import VoiceResponse, <PERSON>ather
from loguru import logger

from audios import DEFAULT_AUDIO_WELCOME_VOCCA

router = APIRouter(prefix="/svi")


@router.post("/{customer}", response_class=PlainTextResponse)
async def svi(request: Request, customer: str):
    svi_config_customer = {
        "somed": {
            "audio_welcome": "https://vocca.s3.amazonaws.com/audio/rene_laborie-20250613-171146.mp3",
            "1": "+33974990844",  # 1er etage
            "2": "+33974990827",  # 5eme etage
            "3": "+33182287053",  # Rene transfert direct
        },
        "somed_clichy": {
            "audio_welcome": "https://vocca.s3.amazonaws.com/audio/audio_2025-06-24_clichy-20250624-104138.mp3",
            "1": "+33974990857",  # Radiologie config135
            "2": "+33974990846",  # Autre config91
        },
        "somed_amiens": {
            "audio_welcome": "https://vocca.s3.amazonaws.com/audio/SVI-Somed-Amiens-20250627-111538.mp3",
            "1": "+33974992551",  # bot
            "2": "+33364260984",  # transfert direct
        },
    }

    svi_config = svi_config_customer.get(customer)
    if not svi_config:
        raise HTTPException(
            status_code=404,
            detail=f"No SVI configuration found for customer: {customer}",
        )

    try:
        data = await request.form()

        caller_phone = data.get("From")
        if caller_phone in [
            "anonymous",
            "+anonymous",
            "+33974368037",
            "Restricted",
            "Anonymous",
            "33",
        ]:
            resp = VoiceResponse()
            resp.say(
                "Bonjour, merci de bien vouloir rappeler sans masquer votre numéro de téléphone.",
                language="fr-FR",
                rate="1.15",
            )
            resp.hangup()
            return PlainTextResponse(content=str(resp), media_type="application/xml")

        digits = data.get("Digits")
        resp = VoiceResponse()

        if digits and digits in svi_config:
            config_svi = svi_config[digits]

            resp.dial(config_svi)
        else:
            url_sound = svi_config.get("audio_welcome", DEFAULT_AUDIO_WELCOME_VOCCA)
            print(url_sound)
            gather = Gather(
                num_digits=1,
                action=f"/svi/{customer}",
                method="POST",
                timeout=10,
                finish_on_key="#",
            )
            gather.play(url_sound)
            resp.append(gather)
            # Optional: repeat or hang up if no input
            resp.say(
                "Nous n'avons pas reçu votre choix. Veuillez réessayer.",
                language="fr-FR",
            )
            resp.hangup()

        return PlainTextResponse(content=str(resp), media_type="application/xml")

    except Exception as e:
        logger.error(f"Error in /svi/{customer}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
