import asyncio
import time
from fastapi import <PERSON>Router, Request, HTTPException
from fastapi.responses import PlainTextResponse, RedirectResponse

from lib.supabase_client import supabase_client
from lib.daily_client import DailyClient
from constants import DEV_CALL_ID

router = APIRouter(prefix="/browser")
import sys


def get_config():
    # Import this function or make it accessible
    from main import get_config

    return get_config()


@router.get("/start_bot/{customer}/{config_id}", response_class=PlainTextResponse)
async def browser_start_bot_with_customer(
    request: Request, customer: str, config_id: str
):
    """
    Dev endpoint to join the dev Daily room and start the bot of a specific customer
    """
    config = get_config()
    daily_client: DailyClient = request.app.state.daily_client
    dev_daily_room = await daily_client.get_dev_daily_room()
    params = request.query_params
    if "new_room" in params:
        dev_daily_room = await daily_client.create_daily_web_room()

    if dev_daily_room is None or config.test:
        dev_daily_room = await daily_client.create_daily_web_room()

    if not daily_client:
        raise HTTPException(status_code=500, detail="DailyUtils not initialized")

    callId = DEV_CALL_ID + str(time.time().as_integer_ratio()[0])
    caller_phone_number = config.phone if config.phone else "dev_caller_phone"
    room_token = await daily_client.get_token(dev_daily_room)

    speed_voice = config.speed_voice if config.speed_voice else "1.15"

    config = get_config()
    if config.test:
        if not config.phone:
            return PlainTextResponse(
                "No phone number provided. Please provide a phone number with --phone or set it in your configuration."
            )

        prompt_id = int(config.test) if config.test else 3
        supabase_client.create_inbound_test_bot_data(dev_daily_room.url, prompt_id)

        asyncio.create_task(daily_client.start_test_bot(dev_daily_room, prompt_id))

    asyncio.create_task(
        daily_client.start_customer_bot(
            customer,
            dev_daily_room,
            callId,
            None,
            config_id,
            caller_phone_number,
            room_token,
            speed_voice,
            is_test=config.test,
        )
    )

    return RedirectResponse(url=dev_daily_room.url)


@router.get("/start_bot/{config_id}")
@router.post("/start_bot/{config_id}")
async def browser_start_bot(request: Request, config_id: str):
    """
    Dev endpoint to join the dev Daily room and start the bot of a specific customer
    """
    params = dict(request.query_params)
    daily_client: DailyClient = request.app.state.daily_client

    callId = DEV_CALL_ID + str(time.time().as_integer_ratio()[0])

    company_data = supabase_client.get_company_data_by_config_id(config_id)
    if not company_data:
        raise HTTPException(status_code=404, detail="Config ID not found")

    customer = company_data.config_name

    dev_daily_room = await daily_client.create_daily_web_room()

    if "phone" not in params:
        print("No phone number provided, using default +33670616191")
        params["phone"] = "+33670616191"

    if "speed_voice" not in params:
        print("No speed_voice provided, using default 1.15")
        params["speed_voice"] = "1.15"

    caller_phone_number = (
        "+" + params["phone"]
        if not params["phone"].startswith("+")
        else params["phone"]
    )
    caller_phone_number = caller_phone_number.replace(" ", "")
    print(f"Caller phone number: {caller_phone_number}")

    if not daily_client:
        raise HTTPException(status_code=500, detail="DailyUtils not initialized")

    room_token = await daily_client.get_token(dev_daily_room)

    speed_voice = params.get("speed_voice", "1.15")
    prompt_id = params.get("prompt_id", None)
    is_test = True if prompt_id else False
    if prompt_id:
        print(f"Using prompt_id: {prompt_id}")
        supabase_client.create_inbound_test_bot_data(dev_daily_room.url, int(prompt_id))
        asyncio.create_task(daily_client.start_test_bot(dev_daily_room, prompt_id))

    asyncio.create_task(
        daily_client.start_customer_bot(
            customer,
            dev_daily_room,
            callId,
            None,
            config_id,
            caller_phone_number,
            room_token,
            speed_voice,
            is_test=is_test,
        )
    )

    # if post than return json
    if request.method == "POST":
        return {
            "room_url": dev_daily_room.url,
            "call_id": callId,
            "caller_phone_number": caller_phone_number,
            "room_token": room_token,
        }

    return RedirectResponse(url=dev_daily_room.url)
