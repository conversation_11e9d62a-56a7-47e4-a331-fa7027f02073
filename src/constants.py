from enum import Enum, unique
import os
from dotenv import load_dotenv

current_dir = os.path.dirname(os.path.abspath(__file__))
load_dotenv(dotenv_path=os.path.join(current_dir, "../.env"))


@unique
class Intents(Enum):
    NOUVEAU = "nouveau"
    MODIFIER = "modifier"
    QUESTION = "question"
    URGENCE = "urgence"
    RETARD = "retard"
    ANNULATION = "annuler"
    CONFIRMER = "confirmer"
    INIT_NODE = "start"


@unique
class GENDER(Enum):
    MALE = False
    FEMALE = True
    UNDEFINED = None


INTENT_TO_TAG = {
    Intents.NOUVEAU: "Nouveau RDV",
    Intents.ANNULATION: "Annulation RDV",
    Intents.MODIFIER: "Modification RDV",
    Intents.QUESTION: "Question/Tâche médicale",
    Intents.URGENCE: "Urgence",
    Intents.RETARD: "Retard",
    Intents.CONFIRMER: "Confirmation RDV",
    Intents.INIT_NODE: "Question/Tâche médicale",
}

@unique
class ClientType(Enum):
    DEFAULT = "default"
    IMAGING = "imaging"


@unique
class METADATA(str, Enum):
    female = "female"


ADULT_AGE = 18


class AGE_CATEGORIES(Enum):
    ENFANT = "enfant"
    ADULT = "adult"

    def __str__(self):
        return self.value

    def __repr__(self):
        return f"AGE_CATEGORIES.{self.name}"


class DAYS_OF_THE_WEEK(Enum):
    """
    DAYS_OF_THE_WEEK['lundi'].value returns 0

    DAYS_OF_THE_WEEK(0).name returns 'lundi'
    """

    lundi = 0
    mardi = 1
    mercredi = 2
    jeudi = 3
    vendredi = 4
    samedi = 5
    dimanche = 6


MAX_SESSION_TIME = 10 * 60
MAX_APPOINTMENT_DAYS = 365
MAX_RETRIES_TAKE_APPOINTMENT = 3

DIFF_HOURS_BETWEEN_SUGGESTED_TIMES = 3


REQUIRED_ENV_VARS = [
    "OPENAI_API_KEY",
    "DAILY_API_KEY",
    "ELEVENLABS_API_KEY",
    "ELEVENLABS_VOICE_ID",
    "SUPABASE_URL",
    "SUPABASE_KEY",
    "SECRET_DOCTOLIB_URL",
    "SECRET_DOCTOLIB_TOKEN",
    "TREMBLAY_OFFICE_CODE",
]

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
DAILY_API_KEY = os.getenv("DAILY_API_KEY")
DAILY_API_URL = os.getenv("DAILY_API_URL")
DAILY_DEV_ROOM = os.getenv("DAILY_DEV_ROOM") or ""
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
CARTESIA_API_KEYS = [os.getenv("CARTESIA_API_KEY"), os.getenv("CARTESIA_API_KEY_2")]
CARTESIA_VOICE_ID = os.getenv("CARTESIA_VOICE_ID")
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")

SECRET_DOCTOLIB_TOKEN = os.getenv("SECRET_DOCTOLIB_TOKEN")
SECRET_DOCTOLIB_URL = os.getenv("SECRET_DOCTOLIB_URL")
TREMBLAY_OFFICE_CODE = os.getenv("TREMBLAY_OFFICE_CODE")

AZURE_SPEECH_API_KEYS = [
    os.getenv("AZURE_SPEECH_KEY_1", ""),
    os.getenv("AZURE_SPEECH_KEY_2", ""),
    os.getenv("AZURE_SPEECH_KEY_3", ""),
    os.getenv("AZURE_SPEECH_KEY_4", ""),
    os.getenv("AZURE_SPEECH_KEY_5", ""),
    os.getenv("AZURE_SPEECH_KEY_6", ""),
    os.getenv("AZURE_SPEECH_KEY_7", ""),
    os.getenv("AZURE_SPEECH_KEY_8", ""),
    os.getenv("AZURE_SPEECH_KEY_9", ""),
    os.getenv("AZURE_SPEECH_KEY_10", ""),
    os.getenv("AZURE_SPEECH_KEY_11", ""),
]
AZURE_REGION = os.getenv("AZURE_REGION")


AZURE_LLM_KEY = os.getenv("AZURE_LLM_KEY")
AZURE_LLM_ENDPOINT = os.getenv("AZURE_LLM_ENDPOINT")

INTERNAL_SERVER_URL = "https://internal-server-8w13.onrender.com"
DEV_CALL_ID = "dev_call_id"
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG")
LOGOS_BASE_URL = "https://rdvdentiste.net/api"
GLADIA_API_KEY = os.getenv("GLADIA_API_KEY")
NODE_ENV = os.getenv("NODE_ENV", "production")

DAILY_FULL_STACK_URL = "http://localhost:7860"


MOTIFS_WITH_CONDITIONS = {
    "config92": {
        259880: "Avez-vous déjà consulté un cardiologue?",
        14096532: "Quelle est la marque de votre pacemaker ou défibrillateur ?",
    },
    "config84": {
        5463033: "Avez-vous l'ordonance pour la crème anesthésiante 'Euh-Emme-Elle-A' qui faudra appliquer 1 heure et demi avant votre rendez-vous ?",
        664255: "Avez-vous l'ordonance pour la crème anesthésiante 'Euh-Emme-Elle-A' qui faudra appliquer 1 heure et demi avant votre rendez-vous ?",
        169376: "Avez-vous l'ordonance pour la crème anesthésiante 'Euh-Emme-Elle-A' qui faudra appliquer 1 heure et demi avant votre rendez-vous ?",
    },
    "config91": {
        152685: "Cette consultation est-elle demandé à la suite d'un accident du travail ? Merci de répondre par oui ou non"
    },
    "config88": {
        152685: "Cette consultation est-elle demandé à la suite d'un accident du travail ? Merci de répondre par oui ou non"
    },
}

SECRET_THERASOFT_URL = os.getenv("SECRET_THERASOFT_URL")
SECRET_EDL_URL = os.getenv("SECRET_EDL_URL")
SECRET_THERASOFT_EDL_TOKEN = os.getenv("SECRET_THERASOFT_EDL_TOKEN")


GITHUB_TOKEN_PR = os.getenv("GITHUB_TOKEN_PR")
