import asyncio
from loguru import logger
from typing import List
from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)
from twilio.twiml.voice_response import VoiceResponse
from pipecat.processors.frame_processor import FrameDirection
from pipecat.frames.frames import <PERSON><PERSON>rame
from nodes.base.intake_processor import BaseIntakeProcessor
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor

from models import CompanyData, PatientData, PhoneCaller, AppointmentForm, Appointment
from lib.booking_provider import BookingProviderFactory
from audios import AUDIO_TRANSFERT_CALL
import utils
import utils.tts

from .sentences import phrases


from constants import (
    Intents,
    MAX_RETRIES_TAKE_APPOINTMENT,
)
from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from .prompts import TeoMedPrompts
from .constants_specific import (
    NUM_HOPITAL,
    MOTIF_ALIAS,
)
from .tools import TeoMedTools


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm=llm,
            context=context,
            company_data=company_data,
            patient_data=patient_data,
            phone_caller=phone_caller,
        )
        self.company_data.all_in_task = True
        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = TeoMedPrompts(company_data)
        self.phrases = phrases

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0

        prompt, tools = self.prompts.init(all_in_task=self.company_data.all_in_task)
        context.set_tools(tools)
        context.add_message(prompt)

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(
            self, self.__switcher_output_contact_form_node
        )
        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self,
            output_function=self.__switcher_output_appointment_form_node,
            min_age=3,
            max_age=69,
            motif_alias=MOTIF_ALIAS,
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))
        appointment_motive = args.get("appointment_motive")

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [TeoMedTools.handle_check_intent],
            )
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm=llm,
            context=context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
                )
                self.phone_caller.successful_call = True
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            find_motive = None
            if appointment_motive:
                find_motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
                    appointment_motive,
                )

            if find_motive:
                self.appointment_form_intake_processor.main_motive_id = (
                    find_motive.visit_motive_id
                )
                self.appointment_form_intake_processor.main_motive = find_motive

            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                    return_node=TeoMedTools.handle_check_intent,
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=TeoMedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=TeoMedTools.handle_check_intent,
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=TeoMedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            prompt, tools = self.prompts.ask_transfer_to_hospital()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        elif intent == Intents.ANNULATION:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=TeoMedTools.handle_check_intent
                )
                context.set_tools(tools)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=TeoMedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=TeoMedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [TeoMedTools.handle_check_intent],
            )
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        if self.phone_caller.intent == Intents.NOUVEAU:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            await self.appointment_form_intake_processor.start_node(
                context, llm, result_callback
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:

            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            await self.forward_call(
                "forward_call",
                None,
                args,
                llm,
                context,
                result_callback,
            )
            return

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_identify():
                self.phone_caller.successful_call = True
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        raise ValueError(f"Intent not {self.phone_caller.intent} not handle")

    async def __switcher_output_contact_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):

        if self.phone_caller.intent == Intents.NOUVEAU:
            await self.appointment_form_intake_processor.start_node(
                context, llm, result_callback
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:
            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        if self.phone_caller.intent == Intents.QUESTION:
            self.phone_caller.has_note = True
            prompt, tools = self.prompts.ask_question(self._patient_data)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    async def __switcher_output_appointment_form_node(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )

    async def handle_transfer_to_hospital(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        """
        Handle transfer to hospital
        """
        confirm = args["transfer"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if confirm:
            await self.forward_call(
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
            return

        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
        )
        return

    async def forward_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        """
        Forward the hospital call
        """

        if self.retries_take_appointment < MAX_RETRIES_TAKE_APPOINTMENT:
            self.phone_caller.successful_call = True

        if self.phone_caller.has_forward_call:
            return

        try:
            if self.phone_caller.intent == Intents.URGENCE:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je vous transfère à l'hôpital. Veuillez patienter un instant.",
                )
                self.phone_caller.has_forward_call = True
                response = VoiceResponse()
                response.play(AUDIO_TRANSFERT_CALL)
                response.dial(NUM_HOPITAL)
                twiml_response = str(response)
                updated_call = await asyncio.to_thread(
                    self.twilio_client.get_client().calls(self.phone_caller.id).update,
                    twiml=twiml_response,
                )
                logger.info(
                    f"Call forwarded to {NUM_HOPITAL}. Updated call status: {updated_call.status}"
                )

                return

            else:
                await self.end_call(
                    function_name,
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        except Exception as e:
            logger.error(f"Failed to play audio or forward call: {str(e)}")
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)
