from pipecat.services.openai.llm import OpenAILLMContext, OpenAILLMService
from nodes.base.intake_processor import BaseIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.appointment_form.tools import AppointmentFormTools
from models import CompanyData, PatientData, PhoneCaller
from lib.booking_provider import BookingProviderFactory
from .prompts import UroarlesPrompts
from datetime import datetime
from .tools import UroarlesTools
from nodes.appointment_form.node_functions import get_main_motive
import asyncio
from twilio.twiml.voice_response import VoiceResponse
from audios import AUDIO_TRANSFERT_CALL
from .nodes_functions import should_create_note
from nodes.appointment_form.node_functions import find_motive_by_id

import utils
import utils.appointments
import utils.tts
import utils.date
from constants import (
    Intents,
    AGE_CATEGORIES,
    ADULT_AGE,
)
from locales import get_locale

phrases = get_locale()

from .constants_specific import (
    SPECIALITIES_MAIN_REASON,
    MINIMUM_AGE,
    MAXIMUM_AGE,
    HOPITAL_NUM,
)


class IntakeProcessor(BaseIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):
        self.booking_provider = BookingProviderFactory.get_provider(company_data)
        super().__init__(company_data, phone_caller, patient_data)
        self.prompts = UroarlesPrompts(company_data)
        self.company_data.all_in_task = True
        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

        self.ask_doctor_name = True

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(
            self, self.__switcher_output_contact_form_node
        )
        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self,
            output_function=self.__switcher_output_appointment_form_node,
            min_age=MINIMUM_AGE,
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_call_urgent_hospital(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        prompt, tools = self.prompts.ask_should_tranfer_to_hospital()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_transfer_to_hospital(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        transfer = args["transfer"]
        transfer = transfer == True or "oui" in str(transfer).lower()
        if transfer:
            await self._say_and_wait(
                llm,
                context,
                "Je vous transfère à l'hôpital.",
            )
            self.phone_caller.has_forward_call = True
            self.phone_caller.successful_call = True
            response = VoiceResponse()
            response.play(AUDIO_TRANSFERT_CALL)
            response.dial(HOPITAL_NUM)
            twiml_response = str(response)
            await asyncio.to_thread(
                self.twilio_client.get_client().calls(self.phone_caller.id).update,
                twiml=twiml_response,
            )
            return

        prompt, tools = self.prompts.ask_last_question()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent_arg = args.get("intent")

        if intent_arg == "parler_au_docteur":
            await self._say_and_wait(
                llm,
                context,
                "Je ne peux pas vous transférer car toutes nos lignes sont occupées. Merci de rappeler plus tard.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        intent = self.INTENT_LOOKUP.get(intent_arg)

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [UroarlesTools.handle_check_intent],
            )
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm=llm,
            context=context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment",
                )
                self.phone_caller.successful_call = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            new_main_motive = (
                await self.booking_provider.get_visit_motive_by_name_or_id(
                    str(SPECIALITIES_MAIN_REASON.get(self.company_data.config))
                )
            )
            new_main_motive = await get_main_motive(
                self._patient_data,
                new_main_motive,
                self.company_data.current_to_premiere_motive,
                self.company_data.adult_to_child_motive,
                self.company_data,
            )
            self.main_motive = new_main_motive
            self.appointment_form_intake_processor.main_motive = self.main_motive

            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=UroarlesTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                    first_sentence="Je vais laisser un message au secrétariat"
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=UroarlesTools.handle_check_intent,
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                if should_create_note(self.phone_caller):
                    self.phone_caller.has_note = True
                    prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                        return_node=UroarlesTools.handle_check_intent,
                        first_sentence="Je vais laisser un message à notre centre médical.",
                    )
                    await self._reply_to_user(
                        result_callback, context, llm, prompt, tools
                    )
                    return
                else:
                    self.phone_caller.successful_call = True
                    await self.end_call(
                        "end_call",
                        None,
                        args,
                        llm,
                        context,
                        result_callback,
                    )
                    return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=UroarlesTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            if should_create_note():
                await self.forward_call(
                    "forward_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return
            else:
                self.phone_caller.successful_call = True
                await self.end_call(
                    "end_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        elif intent == Intents.ANNULATION:
            if self._patient_data.is_new_patient:
                if should_create_note():
                    self.phone_caller.has_note = True
                    await self.contact_form_intake_processor.start_node(
                        context, llm, result_callback
                    )
                    return
                else:
                    self.end_call(
                        "end_call",
                        None,
                        args,
                        llm,
                        context,
                        result_callback,
                    )
                    return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=UroarlesTools.handle_check_intent
                )
                context.set_tools(tools)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = should_create_note()
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                    return_node=UroarlesTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=UroarlesTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                if should_create_note():
                    self.phone_caller.has_note = True
                    prompt, tools = self.prompts.contact_form_prompt.ask_first_name()
                    await self._reply_to_user(
                        result_callback,
                        context,
                        llm,
                        prompt,
                        tools,
                    )
                    return
                else:
                    self.phone_caller.successful_call = True
                    await self.end_call(
                        "end_call", None, args, llm, context, result_callback
                    )
                    return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=UroarlesTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [UroarlesTools.handle_check_intent],
            )
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        if Intents.NOUVEAU == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                if not self._patient_data.birthdate:
                    prompt, tools = (
                        self.prompts.appointment_form_prompt.ask_birthdate_of_patient()
                    )
                    await self._reply_to_user(
                        result_callback, context, llm, prompt, tools
                    )
                    return

                if not utils.date.is_minimum_age(
                    self._patient_data.birthdate, MINIMUM_AGE
                ):
                    await self._say_and_wait(
                        llm,
                        context,
                        "le patient doit avoir au moins 3 ans pour que l'on puisse accepter un rendez-vous.",
                    )
                    await self.end_call(
                        "end_call", None, args, llm, context, result_callback
                    )
                    return

                if not utils.date.is_adult(self._patient_data.birthdate):
                    new_main_motive = (
                        await self.booking_provider.get_visit_motive_by_name_or_id(
                            str(SPECIALITIES_MAIN_REASON.get(self.company_data.config))
                        )
                    )
                    new_main_motive = await get_main_motive(
                        self._patient_data,
                        self.main_motive,
                        self.company_data.current_to_premiere_motive,
                        self.company_data.adult_to_child_motive,
                        self.company_data,
                    )
                    # if the visit motive is 1901660, need to change to 2539144 because
                    # 1901660 does not take care of children
                    if new_main_motive.visit_motive_id == 1901660:
                        new_main_motive.visit_motive_id = 2539144

                    self.main_motive.set(new_main_motive.model_dump())
                    await self.appointment_form_intake_processor.handle_doctor_name(
                        "handle_doctor_name",
                        None,
                        {"doctor_name": "ABRAM Fabien (Arles)"},
                        llm,
                        context,
                        result_callback,
                    )
                    return

                doctors = await self.booking_provider.get_doctors()
                main_doctor = self._patient_data.medecin_historique
                prompt, tools = self.prompts.appointment_form_prompt.ask_doctor_name(
                    doctors, main_doctor
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif Intents.ANNULATION == self.phone_caller.intent:
            self.appointment_form = self._patient_data.next_appointment
            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.MODIFIER == self.phone_caller.intent:
            if self._patient_data.is_identify():

                motive = (
                    find_motive_by_id(
                        self._patient_data.next_appointment.visit_motive_id,
                        self.company_data,
                    )
                    if self._patient_data.next_appointment
                    else None
                )

                if not self._patient_data.next_appointment or not (
                    motive and motive.open
                ):
                    await self.end_call(
                        "end_call", None, args, llm, context, result_callback
                    )
                    return

                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

        elif Intents.RETARD == self.phone_caller.intent:
            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            self.phone_caller.has_note = should_create_note()
            prompt, tools = self.prompts.ask_question(self._patient_data)

        elif Intents.QUESTION == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                self.phone_caller.has_note = should_create_note()
                self.phone_caller.successful_call = True
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

    async def handle_birthdate_of_patient(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        birthdate = args.get("birthdate")

        if not utils.appointments.is_valid_date(birthdate):
            await self._reply_to_user(
                result_callback,
                "Dit juste: 'Désolé, je n'ai pas compris votre date de naissance. Pouvez-vous répéter?'",
                [AppointmentFormTools.handle_birthdate_of_patient],
            )
            return

        if isinstance(birthdate, str):
            birthdate = datetime.strptime(birthdate, "%Y-%m-%d")

        self._patient_data.birthdate = birthdate
        self._patient_data.age_category = (
            AGE_CATEGORIES.ADULT.value
            if utils.appointments.is_minimum_age(birthdate, ADULT_AGE)
            else AGE_CATEGORIES.ENFANT.value
        )

        if not utils.appointments.is_minimum_age(birthdate, MINIMUM_AGE):
            min_age_str = str(MINIMUM_AGE)
            await self._say_and_wait(
                llm,
                context,
                f"Je suis désolé, le patient doit avoir au moins {min_age_str} ans pour prendre rendez-vous.",
            )
            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if not utils.appointments.is_maximum_age(birthdate, MAXIMUM_AGE):
            max_age_str = str(MAXIMUM_AGE + 1)
            await self._say_and_wait(
                llm,
                context,
                f"Je suis désolé, le patient doit avoir moins de {max_age_str} ans pour prendre rendez-vous.",
            )
            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if (
            self._patient_data.is_new_patient
            or self._patient_data.age_category == AGE_CATEGORIES.ENFANT.value
        ):
            new_main_motive = await get_main_motive(
                self._patient_data,
                self.main_motive,
                self.company_data.current_to_premiere_motive,
                self.company_data.adult_to_child_motive,
                self.company_data,
                medecin="Fabien Abram",
            )
            if new_main_motive.visit_motive_id == 1901660:
                new_main_motive.visit_motive_id = 2539144
            self.main_motive.set(new_main_motive.model_dump())

        if self.ask_doctor_name:
            if not utils.date.is_adult(self._patient_data.birthdate):
                await self.appointment_form_intake_processor.handle_doctor_name(
                    "handle_doctor_name",
                    None,
                    {"doctor_name": "Fabien Abram"},
                    llm,
                    context,
                    result_callback,
                )
                return

            doctors = await self.booking_provider.get_doctors()
            main_doctor = self._patient_data.medecin_historique
            prompt, tools = self.prompts.appointment_form_prompt.ask_doctor_name(
                doctors, main_doctor
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        prompt, tools = self.prompts.appointment_form_prompt.ask_motive(
            self.main_motive
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def __switcher_output_contact_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):

        if self.phone_caller.intent == Intents.NOUVEAU:
            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:
            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        self.phone_caller.has_note = should_create_note()
        self.phone_caller.successful_call = True
        if self.phone_caller.intent == Intents.QUESTION:
            prompt, tools = self.prompts.ask_question(self._patient_data)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    async def __switcher_output_appointment_form_node(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )

    async def end_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
        ask_last_question: bool = True,
        return_node=None,
    ):
        """
        Last node of the flow. Ends the call.
        """

        self.phone_caller.successful_call = True

        ask_last_question = True
        if not should_create_note(self.phone_caller):
            ask_last_question = False
            self.phone_caller.has_note = False
            await self._say_and_wait(
                llm,
                context,
                "Nous ne pouvons actuellement pas vous transférer au docteur, merci de réessayer à un autre moment de la journée.",
            )

        if ask_last_question and not self.phone_caller.has_note:
            prompt, tools = self.prompts.ask_last_question(return_node)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        message = "Je vous remercie pour votre appel. Je vous souhaite une excellente journée !"
        await self._say_and_wait(
            llm,
            context,
            message,
        )
