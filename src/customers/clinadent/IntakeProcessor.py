from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor
from nodes.appointment_form.node_functions import get_agendas_ids_to_check
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
    MotiveAppointment,
)
from .prompts import ClinadentPrompts
from nodes.appointment_form.node_functions import find_motive_by_id

from .constants_clinadent import (
    ENTENTE_FINANCIERE_ID,
    MINIMUM_AGE,
)
from lib.booking_provider import BookingProviderFactory
from constants import (
    Intents,
    MAX_RETRIES_TAKE_APPOINTMENT,
)
import utils
import utils.tts
from .tools import ClinadentTools

from typing import List

from locales import get_locale

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
            minimum_age=company_data.bot_configuration.age_minimum,
        )

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = ClinadentPrompts(company_data)
        self.company_data.all_in_task = True

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0
        self.say_name_doctor = True
        self.ask_doctor_name = True

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(self)

        main_motive_ids_specialities = list(
            self.company_data.main_motive_per_speciality.values()
        )
        main_motive_id = (
            main_motive_ids_specialities[0] if main_motive_ids_specialities else None
        )
        print(
            f"Warning: No main motive ID provided, using first speciality's main motive ID: {main_motive_id}"
        )
        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self,
            output_function=self.__switcher_output_appointment_form_node,
            main_motive_id=main_motive_id,
            min_age=MINIMUM_AGE,
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_confirm_motive(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args.get("confirm")
        confirm = confirm == True or "oui" in str(confirm).lower()

        # the user has already confirmed the reason
        if self.appointment_form.start_date:
            confirm = True

        if self.retries_take_appointment > MAX_RETRIES_TAKE_APPOINTMENT:
            self.phone_caller.has_note = True
            await self._say_and_wait(
                llm=llm,
                context=context,
                message="Je notifie le centre médical. Vous serez recontacté sous peu de temps.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if not confirm:
            self.retries_take_appointment += 1
            prompt, tools = self.prompts.appointment_form_prompt.ask_motive()
            context.set_tools(tools)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if (
            self.appointment_form.visit_motive_id
            == ENTENTE_FINANCIERE_ID[self.company_data.config]
            and self._patient_data.is_new_patient
        ):
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, je ne peux pas prendre de rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
            )
            self.phone_caller.successful_call = True
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        found_motive = find_motive_by_id(
            self.appointment_form.visit_motive_id,
            self.company_data,
        )

        if self.appointment_form.is_open_motive:
            """
            Start handle agenda_ids
            """
            self.agenda_ids_to_check = get_agendas_ids_to_check(
                self.company_data,
                self.phone_caller,
                self.appointment_form,
                found_motive,
                self._patient_data,
                self.ask_doctor_name,
            )

            """
            end handle agenda_ids
            """

            await self._say_and_wait(
                llm,
                context,
                "Je vérifie nos disponibilités, un instant s'il vous plaît.",
            )

            self.next_availabilities = (
                await self.doctolib_client.get_next_availabilities(
                    motive=MotiveAppointment(
                        visit_motive_id=self.appointment_form.visit_motive_id,
                        visit_motive_name=self.appointment_form.visit_motive_name,
                        open=self.appointment_form.is_open_motive,
                    ),
                    agenda_ids=self.agenda_ids_to_check,
                )
            )

            if (not self.next_availabilities) or len(self.next_availabilities) == 0:
                await self._say_and_wait(
                    llm,
                    context,
                    "Il n'y a pas de rendez-vous disponible actuellement. Je vous invite à rappeler plus tard de nouveaux créneaux ouvrent chaque jour.",
                )
                self.phone_caller.successful_call = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            prompt, tools = (
                self.prompts.appointment_form_prompt.suggest_appointment_datetime(
                    self.next_availabilities,
                )
            )

            self.appointment_form_intake_processor.next_availabilities = (
                self.next_availabilities
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        else:
            if not self._patient_data.is_identify():
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            await self._say_and_wait(
                llm,
                context,
                "Pour ce type de demande, il n'est malheureusement pas possible de modifier le rendez-vous par téléphone. Votre demande a bien été prise en compte, et un membre de notre équipe d'accueil vous recontactera dans les plus brefs délais.",
            )

            await self.end_call(
                "end_call",
                None,
                args,
                llm,
                context,
                result_callback,
                ask_last_question=True,
            )
            return

    async def __switcher_output_appointment_form_node(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )
