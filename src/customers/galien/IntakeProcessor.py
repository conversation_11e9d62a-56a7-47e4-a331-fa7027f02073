from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from nodes.appointment_form.node_functions import find_motive_by_id

from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
from .prompts import GalienPrompts

from .constant_specific import (
    MINIMUM_AGE,
)
from lib.booking_provider import BookingProviderFactory
from constants import (
    Intents,
)
import utils
import utils.tts
from .tools import GalienTools

from typing import List

from locales import get_locale

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):
        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
            minimum_age=MINIMUM_AGE,
        )

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = GalienPrompts(company_data)
        self.company_data.all_in_task = True

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))
        appointment_motive = args.get("appointment_motive")

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [GalienTools.handle_check_intent],
            )
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm=llm,
            context=context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
                )
                self.phone_caller.successful_call = True
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            if appointment_motive:
                find_motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
                    appointment_motive,
                )
                if find_motive:
                    self.appointment_form_intake_processor.main_motive_id = (
                        find_motive.visit_motive_id
                    )
                    self.appointment_form_intake_processor.main_motive = find_motive

            if self._patient_data.is_new_patient:
                prompt, tools = self.prompts.ask_how_many_motives()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=GalienTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=GalienTools.handle_check_intent,
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=GalienTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            await self.forward_call(
                "forward_call",
                None,
                args,
                llm,
                context,
                result_callback,
            )
            return

        elif intent == Intents.ANNULATION:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=GalienTools.handle_check_intent
                )
                context.set_tools(tools)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=GalienTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=GalienTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                message,
                [GalienTools.handle_check_intent],
            )
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        if self.phone_caller.intent == Intents.NOUVEAU:
            if not self._patient_data.is_identify():
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            prompt, tools = self.prompts.ask_how_many_motives()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:

            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_identify():
                self.phone_caller.successful_call = True
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        raise ValueError(f"Intent not {self.phone_caller.intent} not handle")

    async def handle_how_many_motives(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        """
        Handle the number of motives for the appointment.
        """
        how_many_motives = args.get("number_of_motives", 1)
        SIMPLE_MOTIVE_ID = 14230104
        COMPLEX_MOTIVE_ID = 14230255
        if isinstance(how_many_motives, str):
            how_many_motives = int(how_many_motives)

        motive_id = SIMPLE_MOTIVE_ID if how_many_motives == 1 else COMPLEX_MOTIVE_ID

        motive = find_motive_by_id(
            motive_id,
            self.company_data,
        )
        self.appointment_form_intake_processor.main_motive = motive
        self.appointment_form_intake_processor.main_motive_id = motive_id

        if self._patient_data.is_new_patient:
            await self.contact_form_intake_processor.start_node(
                context, llm, result_callback
            )
            return

        await self.appointment_form_intake_processor.start_node(
            context, llm, result_callback
        )
        return
