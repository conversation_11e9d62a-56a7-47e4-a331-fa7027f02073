from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from lib.booking_provider import BookingProviderType

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
from rapidfuzz import fuzz
from .prompts import EdlPrompts

from .constant_specific import SPECIALITIES_NAMES
from lib.booking_provider import BookingProviderFactory
from constants import (
    Intents,
)
import utils
import utils.tts
from .tools import EDLTools

from typing import List

from locales import get_locale
import logging

logger = logging.getLogger(__name__)

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
        )

        if "centre" not in self.company_data.name.lower():
            self.company_data.name = "Centre d'imagerie " + self.company_data.name
        self.specialities = SPECIALITIES_NAMES.get(self.company_data.config, [])

        company_data.booking_provider = BookingProviderType.EDL

        self.edl_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = EdlPrompts(company_data)
        self.company_data.all_in_task = True

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0
        self.ask_doctor_name = False
        self.say_name_doctor = False

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

        def __init_nodes__(self):
            self.contact_form_intake_processor = ContactFormIntakeProcessor(
                self, self.__switcher_output_contact_form_node
            )

            self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
                self,
                output_function=self.__switcher_output_appointment_form_node,
            )

            functions = {}

            # Add functions from the current class
            for function_name in dir(self.__class__):
                if callable(
                    getattr(self.__class__, function_name)
                ) and not function_name.startswith("_"):
                    functions[function_name] = self

            # Add functions from contact form intake processor (only those that are not already implemented in the current class)
            for function_name in dir(self.appointment_form_intake_processor.__class__):
                if (
                    callable(
                        getattr(
                            self.appointment_form_intake_processor.__class__,
                            function_name,
                        )
                    )
                    and not function_name.startswith("_")
                    and function_name not in functions
                ):
                    functions[function_name] = self.appointment_form_intake_processor

            # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
            for function_name in dir(self.contact_form_intake_processor.__class__):
                if (
                    callable(
                        getattr(
                            self.contact_form_intake_processor.__class__, function_name
                        )
                    )
                    and not function_name.startswith("_")
                    and function_name not in functions
                ):
                    functions[function_name] = self.contact_form_intake_processor

            # Return unique functions as a list of tuples
            return list(functions.items())

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(result_callback, context, llm, message)
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm=llm,
            context=context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
                )
                self.phone_caller.successful_call = True
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            if self._patient_data.is_new_patient:
                prompt, tools = self.prompts.ask_how_many_motives()
                logger.warning(f"asking how many motives for new patient: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=EDLTools.handle_check_intent
                )
                logger.warning(f"asking confirm identity: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                    first_sentence="Je vais laisser un message au secrétariat"
                )
                logger.warning(f"asking first name: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=EDLTools.handle_check_intent,
                )
                logger.warning(f"asking confirm identity: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                    return_node=self.handle_check_intent,
                    first_sentence="Je vais laisser un message à notre centre médical.",
                )
                logger.warning(f"asking first name: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=EDLTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            await self.forward_call(
                "forward_call",
                None,
                args,
                llm,
                context,
                result_callback,
            )
            return

        elif intent == Intents.ANNULATION:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=EDLTools.handle_check_intent
                )
                context.set_tools(tools)
                logger.warning(f"asking confirm identity: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name(
                    return_node=EDLTools.handle_check_intent
                )
                logger.warning(f"asking first name: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=EDLTools.handle_check_intent,
                )
                logger.warning(f"asking confirm identity: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.contact_form_prompt.ask_first_name()
                await self._reply_to_user(
                    result_callback,
                    context,
                    llm,
                    prompt,
                    tools,
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=EDLTools.handle_check_intent
                )
                logger.warning(f"asking first name: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                logger.warning(f"asking confirm identity: {prompt}")
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(result_callback, context, llm, message)
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        logger.warning(f"intent: {self.phone_caller.intent}")
        if self.phone_caller.intent == Intents.NOUVEAU:
            prompt, tools = self.prompts.ask_how_many_motives()
            logger.warning(f"asking how many motives: {prompt}")
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:

            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_identify():
                prompt, tools = self.prompts.ask_question(self._patient_data)
                logger.warning(f"asking question: {prompt}")
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        raise ValueError(f"Intent not {self.phone_caller.intent} not handle")

    async def handle_how_many_motives(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        number_of_motives = int(args.get("number_of_motives"))

        if number_of_motives != 1 and number_of_motives != 2:
            message = "Dit: 'Merci de choisir entre 1 ou 2 examens.'"
            await self._reply_to_user(result_callback, context, llm, message)
            return

        self.appointment_form.number_of_motives = int(number_of_motives)

        # Pour les nouveaux patients, pas de return_node car on veut aller directement à handle_speciality
        if self._patient_data.is_new_patient:
            prompt, tools = self.prompts.ask_speciality()
        else:
            prompt, tools = self.prompts.ask_speciality(
                patient_data=self._patient_data,
                return_node=EDLTools.handle_confirm_identity,
            )

        logger.warning(f"asking speciality: {prompt}")
        await self._reply_to_user(result_callback, context, llm, prompt, tools)

    async def handle_speciality(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):

        arg_speciality = str(args.get("speciality")) if args.get("speciality") else None

        # Find best matching speciality using fuzzy matching
        best_match = None
        best_ratio = 0
        for item in self.specialities:
            items = item.split()

            ratio = fuzz.ratio(
                utils.remove_accents(arg_speciality).lower(),
                utils.remove_accents(item).lower(),
            )

            if len(items) > 1:
                for sub_item in items:
                    ratio = max(
                        ratio,
                        fuzz.ratio(
                            utils.remove_accents(arg_speciality).lower(),
                            utils.remove_accents(sub_item).lower(),
                        ),
                    )

            if ratio > best_ratio:
                best_ratio = ratio
                best_match = item

        if best_ratio < 60:  # Threshold for acceptable match
            speciality_names = [item.lower() for item in self.specialities]
            message = (
                "Dit: Désolé, je ne comprends pas cette spécialité. Veuillez choisir une spécialité parmi les suivantes: "
                + ". ".join(speciality_names)
            )
            self.appointment_form.type = None
            await self._reply_to_user(result_callback, context, llm, message)
            return

        speciality = best_match

        self.appointment_form.type = speciality.lower()

        logger.warning(f"asking appointment form: {context.messages}")
        await self.appointment_form_intake_processor.start_node(
            context, llm, result_callback
        )
        return

    async def __switcher_output_appointment_form_node(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )
