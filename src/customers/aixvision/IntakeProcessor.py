from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
from .prompts import AixV<PERSON>Prompts

from .constant_specific import (
    MINIMUM_AGE,
)
from lib.booking_provider import BookingProviderFactory
from constants import (
    Intents,
)
import utils
import utils.tts
from .tools import AixVision

from typing import List

from locales import get_locale

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
            minimum_age=MINIMUM_AGE,
        )

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = AixVisionPrompts(company_data)
        self.company_data.all_in_task = True

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)
