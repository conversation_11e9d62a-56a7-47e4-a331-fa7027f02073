from models import Mo<PERSON>Appointment, PatientData, <PERSON><PERSON><PERSON><PERSON>


def should_transfer_motive(
        config_id: str,
        motive: MotiveAppointment,
        patient_data: PatientData,
        phone_caller: PhoneCaller,
):
    """
    Check if the appointment should be transferred to the medical center.
    """
    if motive.visit_motive_id in [
        8827834, 8827785, 8827784, 8827786, 9997008, 9997009, 8827976, 8827977, 8827978, 8827979, 9147247, 9147248
    ]:
        return True

    return False
