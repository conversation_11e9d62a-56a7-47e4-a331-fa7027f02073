from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from locales import get_locale
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
)
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from .utils import should_transfer_motive

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):
        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
        )

    def __init_nodes__(self):
        functions = super().__init_nodes__()
        self.appointment_form_intake_processor.should_transfer_motive = should_transfer_motive
        return functions
