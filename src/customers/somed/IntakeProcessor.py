from rapidfuzz import fuzz
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor
from pipecat.services.openai.llm import OpenAILLMContext, OpenAILLMService
from nodes.base.intake_processor import BaseIntakeProcessor
from nodes.base.tools import BaseTools
from models import CompanyData, PatientData, PhoneCaller
from .prompts import SomedPrompts
from .utils import should_transfer_motive

from nodes.appointment_form.node_functions import find_motive_by_id

from .constants_somed import (
    INTAKE_PROCESSOR_CONFIG,
)
from lib.doctolib_client import <PERSON>tolib<PERSON>lient
from constants import (
    Intents,
)
import utils
import utils.tts
import utils.appointments
from .tools import SomedTools

from locales import get_locale

phrases = get_locale()


class IntakeProcessor(BaseIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: <PERSON><PERSON><PERSON>r,
        patient_data: PatientData,
    ):
        self.doctolib_client = DoctolibClient(company_data)
        super().__init__(company_data, phone_caller, patient_data)

        self._patient_data = patient_data
        self.phone_caller = phone_caller
        self.company_data = company_data
        self.company_data.name = company_data.name
        self.company_data.all_in_task = (
            company_data.bot_configuration.all_in_task or False
        )
        self.come_early_instructions = INTAKE_PROCESSOR_CONFIG.get(
            self.company_data.config, {}
        ).get("come_early_instructions", "")

        self.words_boost = company_data.bot_configuration.words_boost_prompt
        print(f"Words boost: {self.words_boost}")

        if "centre" not in self.company_data.name.lower():
            self.company_data.name = "Centre médical " + self.company_data.name
        self.specialities_names = [
            speciality.name.lower()
            for speciality in self.company_data.inbound_config_file.specialities
        ]

        self.adult_to_enfants_consultations = {
            self.company_data.config: self.company_data.adult_to_child_motive
        }
        self.current_to_premieres_consultations = {
            self.company_data.config: self.company_data.current_to_premiere_motive
        }

        self.prompts = SomedPrompts(company_data)
        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(
            self, output_function=self.__switcher_output_contact_form_node
        )

        config = INTAKE_PROCESSOR_CONFIG.get(self.company_data.config, {})

        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self,
            output_function=self.__switcher_output_appointment_form_node,
            motives_ids_with_reasons=config.get("motives_ids_with_reasons", []),
            should_transfer_motive=should_transfer_motive,
            words_boost=self.words_boost,
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))
        appointment_motive = args.get("appointment_motive") # To be used later

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback, context, llm, message, [SomedTools.handle_check_intent]
            )
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm,
            context,
            message=intent_script,
            no_wait=True,
        )

        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at and self.company_data.config not in [
                "config91",  # not blocking somed clichy
                "config135",  # not blocking somed clichy
            ]:

                self.phone_caller.successful_call = True
                self.phone_caller.has_note = self.company_data.config not in [
                    "config88",
                    "config108",
                    "config87",
                ]
                email = None
                if self.company_data.config == "config87":
                    email = utils.tts.convert_email_to_spoken_text(
                        "<EMAIL>"
                    )

                if self.company_data.config in ["config88", "config108"]:
                    email = utils.tts.convert_email_to_spoken_text(
                        "<EMAIL>"
                    )
                    self.phone_caller.has_note = False

                if email:
                    msg = (
                        "Suite à plusieurs rendez-vous non honorés, votre accès à la prise de rendez-vous est temporairement bloqué. "
                        f"Pour être débloqué et pouvoir reprendre un rendez-vous, merci de nous écrire à l'adresse suivante : {email}"
                    )
                    await self._say_and_wait(llm, context, msg, no_wait=True)
                else:
                    await self._say_and_wait(
                        llm,
                        context,
                        "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment.",
                    )

                if self.phone_caller.has_note:
                    await self._say_and_wait(
                        llm,
                        context,
                        "Je vais laisser un message à notre centre médical.",
                    )

                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            # if appointment_motive:
            #     find_motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
            #         appointment_motive,
            #     )
            #     if find_motive:
            #         self.appointment_form_intake_processor.main_motive_id = (
            #             find_motive.visit_motive_id
            #         )
            #         self.appointment_form_intake_processor.main_motive = find_motive

            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=SomedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if not self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=SomedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
            else:
                if self.company_data.bot_configuration.all_in_task:
                    await self.appointment_form_intake_processor.start_node(
                        context,
                        llm,
                        result_callback,
                        return_node=SomedTools.handle_check_intent,
                    )
                    return

                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=SomedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            await self.forward_call(
                "forward_call", None, args, llm, context, result_callback
            )
            return

        elif intent == Intents.ANNULATION:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                    return_node=BaseTools.handle_check_intent,
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=SomedTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                    return_node=BaseTools.handle_check_intent,
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                    return_node=BaseTools.handle_check_intent,
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=SomedTools.handle_check_intent
                )
                await self._reply_to_user(
                    result_callback,
                    context,
                    llm,
                    prompt,
                    tools,
                )
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(result_callback, message)
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        if confirm and self._patient_data.is_identify():
            next_appointments = self._patient_data.appointments
            if next_appointments:
                next_appointments_str = (
                    "Si le patient demande: Ses prochains rendez-vous sont : "
                    + ", ".join(
                        f"{appt.visit_motive_name} le {appt.start_date.strftime('%d/%m/%Y à %Hh%M')}"
                        for appt in next_appointments
                    )
                    + "."
                )
            else:
                next_appointments_str = (
                    "Si le patient demande: Le patient n'a pas de rendez-vous prévu"
                )

            context.add_message({"role": "assistant", "content": next_appointments_str})

        if Intents.NOUVEAU == self.phone_caller.intent:
            if not self._patient_data.is_identify():
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            is_blocked = utils.appointments.is_patient_blocked_due_to_no_show(
                self._patient_data, self.company_data
            )

            if is_blocked:
                if self.company_data.config == "config87":
                    email = utils.tts.convert_email_to_spoken_text(
                        "<EMAIL>"
                    )

                if self.company_data.config in ["config88", "config108"]:
                    email = utils.tts.convert_email_to_spoken_text(
                        "<EMAIL>"
                    )
                    self.phone_caller.has_note = False

                msg = (
                    "Suite à plusieurs rendez-vous non honorés, votre accès à la prise de rendez-vous est temporairement bloqué. "
                    f"Pour être débloqué et pouvoir reprendre un rendez-vous, merci de nous écrire à l'adresse suivante : {email}"
                )
                await self._say_and_wait(llm, context, msg, no_wait=True)

                await self.end_call(
                    "end_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                    ask_last_question=False,
                )
                return

            await self.appointment_form_intake_processor.start_node(
                context, llm, result_callback, return_node=BaseTools.handle_check_intent
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:
            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            self.phone_caller.has_note = True
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical, il vous recontactera au plus vite",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_identify():
                self.phone_caller.successful_call = True
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        raise ValueError(f"Intent not {self.phone_caller.intent} not handle")

    """
    
    Situation Confirmation de rendez-vous pour mettre "oui" sur doctolib
    """

    async def __switcher_output_contact_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):

        if self.phone_caller.intent in [
            Intents.NOUVEAU,
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:
            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        if self.phone_caller.intent == Intents.QUESTION:
            prompt, tools = self.prompts.ask_question(self._patient_data)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    async def __switcher_output_appointment_form_node(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        if (
            self.phone_caller.intent in [Intents.NOUVEAU, Intents.MODIFIER]
            and self.appointment_form.is_booking_created
            and self.come_early_instructions
        ):
            await self._say_and_wait(llm, context, self.come_early_instructions)

        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )
        return
