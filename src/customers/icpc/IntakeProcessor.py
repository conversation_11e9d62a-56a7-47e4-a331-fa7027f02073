from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
from .prompts import ICPCPrompts


from lib.booking_provider import BookingProviderFactory
from lib.supabase_client import supabase_client
from constants import (
    Intents,
)
from loguru import logger
from .tools import ICPCTools

from typing import List
import asyncio
from locales import get_locale

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
        )

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.appointment_form = AppointmentForm(config=company_data.config)
        self.prompts = ICPCPrompts(company_data)

        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0

        self.etablissements = supabase_client.get_etablissements(
            self.company_data.config_name
        )

        prompt, tools = self.prompts.ask_etablissements(self.etablissements)
        context.set_tools(tools)
        context.add_message(prompt)
        context.set_tools(tools)

    async def handle_etablissement(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        etablissement_id = args.get("etablissement_id", None)
        if etablissement_id is None:
            prompt, tools = self.prompts.ask_etablissements(self.etablissements)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        company_data = supabase_client.get_company_data_by_config_id(etablissement_id)
        if not company_data:
            logger.error(
                f"Company data not found for etablissement_id: {etablissement_id}"
            )

        prompt, tools = self.prompts.init_patient()
        super().__init__(
            llm,
            context,
            phone_caller=self.phone_caller,
            company_data=company_data,
            patient_data=self._patient_data,
        )

        await self._reply_to_user(result_callback, context, llm, prompt, tools)
