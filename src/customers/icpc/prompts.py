from models import CompanyData
from nodes.base.prompts import BasePrompts
from nodes.appointment_form.prompts import AppointmentFormPrompts
from nodes.contact_form.prompts import ContactFormPrompts
from .tools import ICPCTools
from typing import Dict


class ICPCPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data
        self.appointment_form_prompt = AppointmentFormPrompts(self.company_data)
        self.contact_form_prompt = ContactFormPrompts(self.company_data)

    def ask_etablissements(self, etablissements: Dict[str, str]):

        # etablisssement is a dict with key (id) and value (name)

        etablissements_str = "\n".join(
            [f"{key}: {value}" for key, value in etablissements.items()]
        )

        message = self._set_message(
            f"""
                🗣️ Tu dois prononcer **exactement** cette phrase, sans aucun changement, ajout ou reformulation :
                👉 "Bienvenue au {self.company_data.config_name.lower()}. Quelle établissement souhaitez-vous contacter ?"
                -- 
                
                La liste des établissements est la suivante si le patient te le demande:
                structure: key: name of etablissement
                {etablissements_str}
                
                Après que le patient a parlé:
                Mets dans la variable `etablissement_id` l'id de l'établissement choisi par le patient.
                Si le patient ne sait pas, mets `None` dans la variable `etablissement_id
                            
          '"""
        )

        tools = [ICPCTools.handle_etablissement]
        return message, tools

    def init_patient(self):
        return self.appointment_form_prompt.init()

    def _set_message(self, message: str):
        message = " ".join(
            [line.strip() for line in message.split("\n") if line.strip()]
        )
        return {"role": "system", "content": message}
