from nodes.base.tools import BaseTools
from nodes.appointment_form.tools import AppointmentFormTools
from nodes.contact_form.tools import ContactFormTools


create_tool_description = BaseTools.create_tool_description


class ICPCTools(BaseTools):
    def __init__(self):
        super().__init__()
        self.appointment_form_prompt = AppointmentFormTools()
        self.contact_form_prompt = ContactFormTools()

    handle_etablissement = create_tool_description(
        name="handle_etablissement",
        description="Utilise cette fonction pour utiliser l'établissement choisi par le patient",
        properties=[
            {
                "name": "etablissement_id",
                "type": "string",
                "description": "L'id de l'établissement choisi par le patient. Si le patient ne sait pas, mets `None`.",
                "required": True,
            }
        ],
    )
