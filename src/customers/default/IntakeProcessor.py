from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from locales import get_locale
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
)
from nodes.customer_base.intake_processor import CustomerIntakeProcessor

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
            self,
            llm: OpenAILLMService,
            context: OpenAILLMContext,
            company_data: CompanyData,
            phone_caller: PhoneCaller,
            patient_data: PatientData,
    ):
        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
            minimum_age=company_data.bot_configuration.age_minimum,
        )
