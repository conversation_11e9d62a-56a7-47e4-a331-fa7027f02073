import asyncio
from datetime import datetime
from loguru import logger
from pipecat.transports.services.daily import DailyTransport
from pipecat.services.openai.base_llm import OpenAILLMContextFrame
from pipecat.pipeline.task import PipelineTask
from nodes.base.intake_processor import BaseIntakeProcessor
from models import PhoneCaller, CompanyData
from lib.n8n_client import n8n_client
from lib.twilio_client import TwilioClient
from utils.tts import generate_transcript, convert_email_to_spoken_text
import traceback
import requests
from constants import DAILY_FULL_STACK_URL


async def on_dialin_ready(
    task: PipelineTask,
    twilio_client: TwilioClient,
    sipUri: str,
    phone_caller: PhoneCaller,
):
    """
    Triggers when the dial-in is ready with twilio
    """
    # For Twilio, Telnyx, etc. You need to update the state of the call
    # and forward it to the sip_uri..
    call = await twilio_client.redirect_call_if_in_progress(phone_caller, sipUri)

    if not call:
        logger.warning("Failed to redirect the call to SIP URI.")
        n8n_client.send_error_server(
            config=phone_caller.intent,
            phone_caller=phone_caller,
            traceback="Failed to redirect the call to SIP URI.",
        )
        await task.cancel()


async def on_first_participant_joined(
    transport: DailyTransport,
    participant,
    task: PipelineTask,
    context: OpenAILLMContextFrame,
    company_data: CompanyData,
):
    await transport.capture_participant_transcription(participant["id"])
    current_date = datetime.now().strftime("%d %B %Y")
    company_name = company_data.name
    email_in_text = convert_email_to_spoken_text(company_data.email)
    # global prompt
    context.add_message(
        {
            "role": "system",
            "content": f""" 
              Règles quand tu parles:
              - Tous les noms et prénoms doivent être écrits en minuscules.
              - Ne jamais prononcer le contenu entre parenthèses.
              - Tous les chiffres doivent être exprimés en toutes lettres.
              - Quand dans le prompt y a le mot 'dit' ou 'dis', tu dois dire exactement ce qu'il y entre les guillemets.


              Nous sommes le {current_date}. 
              Vous vous comporterez comme Marie, assistante agréable et professionnelle dans le {company_name}.
              Assurez-vous de rester poli, professionnel et d'offrir une expérience patient agréable tout au long de la conversation.
              Ne dis bonjour qu'une seule fois.
              ** Tu ne demanderas pas le numéro de téléphone du patient car tu vois déjà avec quelle numéro il/elle appelle. **
              Ne conseille jamais de contacter le centre ou le secrétariat.
              Si les réponses de l'utilisateur semblent confuses c'est parce qu'elles viennent d'une retranscription téléphonique n'hésite pas à interpréter les transcriptions.
              Ne donne pas de conseils médicaux. Vous parlez français. Adresse du centre : {company_data.address}.
              Email du secrétariat à écrire comme cela : {email_in_text}.
              Si le patient souhaite parler à un humain, explique que personne n'est disponible pour le moment mais que tu peux prendre en note les demandes du patient pour être recontacté plus tard.
              Horaires du centre : {company_data.openings}. {company_data.additional_information}
              “”"
              ---
             """,
        }
    )
    await asyncio.sleep(2)
    await task.queue_frames([OpenAILLMContextFrame(context)])


async def on_participant_left(
    twilio_client: TwilioClient,
    intake: BaseIntakeProcessor,
    reason,
    is_production: bool,
    task: PipelineTask,
    context: OpenAILLMContextFrame,
):
    """
    Triggers when the participant leaves the room
    """

    intake.phone_caller.reason_for_leaving = reason
    intake.phone_caller.conversation_transcript = generate_transcript(context)

    try:
        intake.phone_caller.recording_url = await twilio_client.fetch_recording_url(
            intake.phone_caller.id
        )
    except Exception as e:
        logger.error(f"Error fetching recording URL: {str(e)}")
        intake.phone_caller.recording_url = None

    try:
        if is_production or intake.company_data.is_test:
            n8n_client.report_calls(
                intake.company_data,
                intake.phone_caller,
                intake._patient_data,
                intake.appointment_form,
            )

        else:
            if not is_production:
                logger.warning(
                    "Probably in local mode, not sending to n8n, so no task created"
                )
                logger.warning(
                    {
                        "phone_caller": intake.phone_caller.model_dump(),
                        "patient_data": intake._patient_data.model_dump(),
                        "appointment_form": intake.appointment_form.model_dump(),
                    }
                )
                logger.info(f"Has note: {intake.phone_caller.has_note}, ")
            if not intake.phone_caller.intent:
                logger.warning("No intent, not sending to n8n")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        n8n_client.send_error_server(
            config=intake.company_data.config,
            phone_caller=intake.phone_caller,
            traceback=traceback.format_exc(),
        )
        traceback.print_exc()

    try:
        requests.post(
            f"{DAILY_FULL_STACK_URL}/status/decrement_running_clients",
            json={"call_id": intake.phone_caller.id},
        )
    except Exception as e:
        logger.error(f"Error decrementing running clients: {str(e)}")

    await task.cancel()


async def on_participant_left_legacy(
    twilio_client: TwilioClient,
    intake,
    reason,
    task: PipelineTask,
):

    try:
        logger.info(f"Reason for leaving: {reason}")

        call_duration = datetime.now() - intake._call_start_time
        logger.info(f"Call duration: {call_duration}")

        transcript = generate_transcript(intake._context)
        logger.info(f"Conversation transcript:\n{transcript}")

        from twilio.base.exceptions import TwilioRestException

        async def __fetch_recording_url(twilioclient, call_id):
            try:
                recordings = twilioclient.recordings.list(call_sid=call_id)
                if recordings:
                    recording_uri = recordings[0].uri
                    recording_url = f"https://api.twilio.com{recording_uri}".replace(
                        ".json", ".mp3"
                    )
                    return recording_url
            except TwilioRestException as e:
                logger.error(f"Failed to fetch recording: {str(e)}")

            return None

        recording_url = await __fetch_recording_url(
            twilio_client.client, intake._callId
        )

        call_data = {
            "twilio_call_id": intake._callId,
            "config": intake._config,
            "recording_url": recording_url,
            "patient_phone_number": intake._caller_phone_number,
            "participant_id": "",
            "reason_for_leaving": reason,
            "call_duration": str(call_duration),
            "patient_data": intake._patient_data,
            "appointment_data": intake._appt_data,
            "conversation_transcript": transcript,
            "call_forwarded": intake._call_forwarded,
            "new_patient": intake._new_patient,
        }

        for key, value in call_data.items():
            logger.info(f"{key}: {value}")

        supa_webhook = "/d6659d2b-38e1-4050-bc8a-7f388d4d408d"
        n8n_client._post(
            endpoint=supa_webhook,
            json=call_data,
        )

        logger.info("Call session completed and data sent to webhook")
        try:
            response = requests.post(
                f"{DAILY_FULL_STACK_URL}/status/decrement_running_clients",
                json={"call_id": intake._callId},
            )
        except Exception as e:
            logger.error(f"Error decrementing running clients: {str(e)}")
        logger.debug("Decrement running clients response: ", str(response))

    except Exception as e:
        logger.error(f"Error in on_participant_left: {str(e)}")
        traceback.print_exc()


async def on_call_state_updated(
    state,
    task: PipelineTask,
):
    """
    Triggers when the call state is updated
    """
    if state == "left":
        await task.stop_when_done()
        logger.info("Call state updated: left")


async def on_idle_timeout(
    twilio_client: TwilioClient,
    intake: BaseIntakeProcessor,
    reason,
    task: PipelineTask,
    context: OpenAILLMContextFrame,
):
    company_data = intake.company_data
    phone_caller = intake.phone_caller

    intake.phone_caller.reason_for_leaving = reason
    intake.phone_caller.conversation_transcript = generate_transcript(context)

    try:
        intake.phone_caller.recording_url = await twilio_client.fetch_recording_url(
            intake.phone_caller.id
        )
    except Exception as e:
        logger.error(f"Error processing phone caller data: {str(e)}")
        intake.phone_caller.recording_url = None

    addional_messages = ""
    try:
        if intake.phone_caller.sip or intake.company_data.is_test:
            n8n_client.report_calls(
                intake.company_data,
                intake.phone_caller,
                intake._patient_data,
                intake.appointment_form,
            )
    except Exception as e:
        logger.error(f"Error reporting calls: {str(e)}")

    logger.info("Conversation has been idle for 2 minute")
    if not phone_caller.successful_call:
        n8n_client.send_error_server(
            config=company_data.config,
            phone_caller=phone_caller,
            traceback="Error: Conversation has been idle for 2 minute"
            + addional_messages,
        )
    try:
        # Then end the conversation gracefully
        response = requests.post(
            f"{DAILY_FULL_STACK_URL}/status/decrement_running_clients",
            json={"call_id": intake.phone_caller.id},
        )
    except Exception as e:
        logger.error(f"Error decrementing running clients: {str(e)}")

    logger.debug("Decrement running clients response: ", str(response))
    await task.stop_when_done()
