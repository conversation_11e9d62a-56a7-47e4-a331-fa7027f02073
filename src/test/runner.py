import asyncio
import sys
import argparse
from pipecat.audio.vad.vad_analyzer import VADParams
from pipecat.audio.vad.silero import SileroVADAnalyzer
import aiohttp


sys.path.append("..")  # Adjust the path to import from the parent directory
sys.path.append(".")  # Adjust the path to import from the current directory

from pipecat.transports.services.daily import (
    DailyTransport,
)
from pipecat.services.openai.llm import (
    OpenAILLMContext,
)
from openai import NOT_GIVEN


import processors

from pipecat.services.azure.common import Language

from pipecat.transports.services.daily import (
    DailyParams,
    DailyTransport,
    DailyTranscriptionSettings,
)
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from lib.supabase_client import supabase_client
import constants

import asyncio
from .prompts import TestPrompts


async def test_main(
    room_url: str,
    token: str,
    init_prompt: str,
):
    #################################
    # PipeCat Pipeline
    #################################
    async with aiohttp.ClientSession() as _:
        transport = DailyTransport(
            room_url,
            token,
            f"Test Chatbot",
            DailyParams(
                api_key=constants.DAILY_API_KEY,
                audio_in_enabled=True,
                audio_out_enabled=True,
                transcription_enabled=True,
                # # Daily
                transcription_settings=DailyTranscriptionSettings(
                    language=Language.FR,
                ),
                vad_analyzer=SileroVADAnalyzer(
                    params=VADParams(
                        confidence=0.5,
                        start_secs=0.2,  # Default is 0.2 seconds
                        stop_secs=4,  # Default is 0.8 seconds
                        min_volume=0.2,
                    )
                ),
            ),
        )

        stt = processors.init_stt_service("azure")
        llm = processors.init_llm_service("openai", NOT_GIVEN)
        tts = processors.init_tts_service(
            "azure", rate="1.5", voice="fr-FR-HenriNeural"
        )

        messages = []
        context = OpenAILLMContext(messages=messages)
        context_aggregator = llm.create_context_aggregator(context)
        test_prompt = TestPrompts(init_prompt)
        context.add_message(test_prompt.message)

        pipeline = Pipeline(
            [
                transport.input(),
                stt,
                context_aggregator.user(),
                llm,
                tts,
                transport.output(),
                context_aggregator.assistant(),
            ]
        )

        task = PipelineTask(
            pipeline,
            params=PipelineParams(allow_interruptions=False),
            idle_timeout_secs=60,  # Set idle timeout to 60 seconds
            cancel_on_idle_timeout=True,
        )

        # Has to stay at the end of the main function
        runner = PipelineRunner()
        await runner.run(task)


from requests import request


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test chatbot with Daily room")
    parser.add_argument(
        "--url",
        type=str,
        default=constants.DAILY_DEV_ROOM,
        help="Room URL to connect to (default: your Daily dev room URL)",
    )
    parser.add_argument(
        "--token",
        type=str,
        required=True,
        help="Token for the Daily room (required)",
    )

    parser.add_argument(
        "--prompt_id", type=int, default=2, help="Test number to run (default: 1)"
    )

    args = parser.parse_args()

    async def run():
        prompt_id = int(args.prompt_id)
        if not prompt_id:
            raise ValueError("Prompt ID is required. Please provide a valid prompt ID.")

        row_prompt = supabase_client.get_inbound_test_prompt_by_id(int(prompt_id))

        if not row_prompt:
            raise ValueError(
                f"No prompt found for ID: {args.prompt_id}. Please check the database."
            )

        await test_main(
            room_url=args.url,
            token=args.token,
            init_prompt=row_prompt.prompt,
        )

    asyncio.run(run())
