from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from pydantic import field_serializer, model_validator
from typing import Dict, List
from .appointment_form import AppointmentForm
from .doctor import Doctor
from constants import GENDER


class PatientData(BaseModel):
    is_new_patient: bool
    id: Optional[str] = None
    patient_id: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    birthdate: Optional[datetime] = None
    city: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    age_category: Optional[str] = None
    bounced_at: Optional[str] = None
    """If there is a datetime value, it means that the patient is not allowed to book an appointment"""
    next_appointment: Optional[AppointmentForm] = None
    last_appointment: Optional[AppointmentForm] = None
    appointments: List[AppointmentForm] = []
    medecin_historique: Optional[Doctor] = None
    historical_doctors: Optional[Dict[int, Doctor]] = {}
    """Historical Doctor per speciality_id """
    note_to_doctor: Optional[str] = None
    num_no_show: Optional[int] = 0
    gender: Optional[GENDER] = GENDER.UNDEFINED

    def set(self, new_data: "PatientData") -> None:
        self.is_new_patient = new_data.is_new_patient
        self.id = new_data.id
        self.first_name = new_data.first_name
        self.last_name = new_data.last_name
        self.birthdate = new_data.birthdate
        self.city = new_data.city
        self.email = new_data.email
        self.phone_number = new_data.phone_number
        self.age_category = new_data.age_category
        self.bounced_at = new_data.bounced_at
        self.next_appointment = new_data.next_appointment
        self.last_appointment = new_data.last_appointment
        self.appointments = new_data.appointments
        self.medecin_historique = new_data.medecin_historique
        self.historical_doctors = new_data.historical_doctors
        self.note_to_doctor = new_data.note_to_doctor
        self.patient_id = new_data.patient_id
        self.gender = new_data.gender

    @field_serializer("birthdate")
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        return dt.strftime("%Y-%m-%d") if dt else None

    @field_serializer("gender")
    def serialize_gender(self, gender: GENDER) -> bool | None:
        return gender.value

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if values is None:
            return values
        if "birthdate" in values and isinstance(values["birthdate"], str):
            values["birthdate"] = datetime.strptime(values["birthdate"], "%Y-%m-%d")
        if "medecin_historique" in values and isinstance(
            values["medecin_historique"], dict
        ):
            values["medecin_historique"] = Doctor(**values["medecin_historique"])

        if "historical_doctors" in values and isinstance(
            values["historical_doctors"], dict
        ):
            values["historical_doctors"] = {
                str(k): Doctor(**v) for k, v in values["historical_doctors"].items()
            }

        return values

    def has_completed_form(self) -> bool:
        return self.first_name is not None and self.last_name is not None

    def is_identify(self) -> bool:
        return self.id or self.has_completed_form()

    def reset(self) -> bool:
        self.is_new_patient = True
        self.id = None
        self.first_name = None
        self.last_name = None
        self.birthdate = None
        self.email = None
        self.age_category = None
        self.next_appointment = None
        self.last_appointment = None
        self.appointments = []
        self.medecin_historique = None
        self.historical_doctors = {}
        self.gender = GENDER.UNDEFINED
        return True
