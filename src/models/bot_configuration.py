from typing import Optional, List, Dict
from pydantic import BaseModel


class BotConfiguration(BaseModel):
    config: str
    say_name_doctor: bool = False
    ask_name_doctor: bool = False
    motives_ids_with_reasons: Optional[List[int]] = None
    instructions_at_end: Optional[str] = None
    age_minimum: Optional[int] = 0
    age_maximum: Optional[int] = 120
    all_in_task: bool = False
    words_boost_prompt: Optional[Dict[str, str]] = {}
    ask_speciality: bool = False
