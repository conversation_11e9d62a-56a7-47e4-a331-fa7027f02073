from pydantic import BaseModel, field_serializer, model_validator, Field
from typing import Optional, List


class MotiveAppointment(BaseModel):
    visit_motive_id: int
    visit_motive_name: str
    open: bool
    price: Optional[str] = None
    instructions: Optional[str] = ""
    after: Optional[str] = None
    before: Optional[str] = None
    type: Optional[str] = ""
    age_minimum: Optional[float] = None
    age_maximum: Optional[float] = None
    "If not age_minimum, the motive is available for all ages. If age_minimum is set and the age is lower than this value, the motive is closed."
    speciality_id: Optional[int] = None
    medecin: Optional[str] = None
    metadata: Optional[List[str]] = Field(default_factory=list)

    @field_serializer("instructions", "type")
    def serialize_str(self, value: Optional[str]) -> Optional[str]:
        return value if value else ""

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if "id" in values:
            values["visit_motive_id"] = str(values.pop("id"))

        if "name" in values:
            values["visit_motive_name"] = str(values.pop("name"))

        return values

    def set(self, motive: dict) -> None:
        self.visit_motive_id = motive.get("visit_motive_id")
        self.visit_motive_name = motive.get("visit_motive_name")
        self.open = motive.get("open")
        self.price = motive.get("price")
        self.instructions = motive.get("instructions")
        self.after = motive.get("after")
        self.before = motive.get("before")
        self.type = motive.get("type")
        self.age_minimum = motive.get("age_minimum")
        self.age_maximum = motive.get("age_maximum")
