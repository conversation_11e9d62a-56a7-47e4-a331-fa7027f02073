from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional


class Appointment(BaseModel):
    agenda_id: int
    medecin: str
    start_date: datetime
    end_date: datetime
    steps: Optional[List[dict]] = []
    modalite_id: Optional[int | str] = None
    visit_motive_id: Optional[int | str] = None
    equipment_agenda_id: Optional[int | str] = None


class AppointmentWithMotive(Appointment):
    visit_motive_id: int
