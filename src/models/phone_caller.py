from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from pydantic import field_serializer
from constants import Intents


class PhoneCaller(BaseModel):
    id: str
    """Twilio id"""
    phone_number: str
    intent: Optional[Intents] = None
    start_time: datetime = datetime.now()
    reason_for_leaving: Optional[str] = None
    recording_url: Optional[str] = None
    conversation_transcript: Optional[str] = None
    has_forward_call: bool = False
    has_note: bool = False
    """Should the bot leave a note for the medical staff"""
    successful_call: bool = False
    sip: Optional[str] = None
    room_url: Optional[str] = None

    @field_serializer("intent")
    def serialize_enum(self, intent: Optional[Intents]) -> Optional[str]:
        if isinstance(intent, Intents):
            return intent.value
        return intent if intent else None

    @field_serializer("start_time")
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat()
