from pydantic import BaseModel
from typing import Optional


class InboundTestLogs(BaseModel):
    room_url: str
    prompt_id: int
    call_id: Optional[str]
    task_full_name: Optional[str]
    task_tag: Optional[str]
    task_text: Optional[str]


class UpdateInboundTestLogs(BaseModel):
    room_url: str
    call_id: Optional[str] = None
    task_full_name: Optional[str] = None
    task_tag: Optional[str] = None
    task_text: Optional[str] = None


class InboundTestPrompts(BaseModel):
    id: int
    title: str
    prompt: str
    created_at: str
