from pydantic import BaseModel, field_serializer
from typing import Optional, Union, List
from datetime import datetime
from .motive_appointment import MotiveAppointment


class AppointmentForm(BaseModel):
    config: str
    id: Optional[str] = None
    visit_motive_id: Optional[int | str] = None
    agenda_id: Optional[int | str] = None
    medecin: Optional[str] = None
    visit_motive_name: Optional[str] = None
    instructions: Optional[str] = ""
    is_open_motive: Optional[bool] = None
    type: Optional[str] = ""
    speciality_id: Optional[int | str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_booking_created: Optional[bool] = None
    patient_id: Optional[Union[int, str]] = None
    notes: Optional[str] = ""
    practitioner_id: Optional[Union[int, str]] = None
    equipment_agenda_id: Optional[Union[int, str]] = None
    steps: Optional[List[dict]] = []
    number_of_motives: Optional[int] = 0
    steps_motives: Optional[List[MotiveAppointment]] = []
    modalite_id: Optional[int | str] = None


    @field_serializer("start_date", "end_date")
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        return dt.isoformat() if dt else ""

    @field_serializer("instructions", "type")
    def serialize_str(self, value: Optional[str]) -> Optional[str]:
        return value if value else ""

    def reset(self) -> bool:
        self.id = None
        self.visit_motive_id = None
        self.agenda_id = None
        self.medecin = None
        self.visit_motive_name = None
        self.instructions = None
        self.is_open_motive = None
        self.type = None
        self.speciality_id = None
        self.start_date = None
        self.end_date = None
        self.is_booking_created = False
        self.patient_id = None
        self.notes = ""
        return True
