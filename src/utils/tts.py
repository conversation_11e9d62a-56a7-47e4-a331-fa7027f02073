from pipecat.services.openai.llm import OpenAILLMContext
from constants import Intents
from models import AppointmentForm, Doctor


def convert_email_to_spoken_text(email):
    """Convert email address to spoken French text representation."""
    special_chars = {"@": "arobase", ".": "point", "-": "tiret", "_": "tiret du bas"}

    # Replace special characters with spaces and their spoken form
    text = email
    for char, replacement in special_chars.items():
        text = text.replace(char, f" {replacement} ")

    return " ".join(text.split())


def generate_transcript(context: OpenAILLMContext):
    messages = context.messages
    transcript = []
    for message in messages:
        if message["role"] == "system" or message["role"] == "tool":
            continue
        role = "Assistant" if message["role"] == "assistant" else "User"
        if message.get("content"):
            transcript.append(f"{role}: {message['content']}")
    return "\n".join(transcript)


from pipecat.services.openai.llm import OpenAILLMService
from pipecat.frames.frames import TTSStartedFrame, TTSSpeakFrame, TTSStoppedFrame


async def say_something(llm: OpenAILLMService, text: str):
    await llm.push_frame(TTSStartedFrame())
    await llm.push_frame(TTSSpeakFrame(text=text))
    await llm.push_frame(TTSStoppedFrame())

def get_intent_description(intent: Intents) -> str:
    if intent == Intents.ANNULATION:
        return "annulation de rendez-vous"
    elif intent == Intents.MODIFIER:
        return "modification de rendez-vous"
    elif intent == Intents.NOUVEAU:
        return "nouveau rendez-vous"
    elif intent == Intents.CONFIRMER:
        return "confirmation de rendez-vous"
    elif intent == Intents.RETARD:
        return "retard"
    elif intent == Intents.QUESTION:
        return "question"
    elif intent == Intents.URGENCE:
        return "urgence"
    else:
        return intent.value

def get_intent_sentence(intent: Intents) -> str:
    if intent == Intents.ANNULATION:
        intent_script = "Très bien, je vais vous aider à annuler un rendez-vous"
    elif intent == Intents.MODIFIER:
        intent_script = "Très bien, je vais vous aider à modifier votre rendez-vous"
    elif intent == Intents.NOUVEAU:
        intent_script = "Très bien, je vais vous aider à planifier ce rendez-vous"
    elif intent == Intents.CONFIRMER:
        intent_script = "Très bien, je vais vous aider à confirmer votre rendez-vous"
    elif intent == Intents.RETARD:
        intent_script = "Je comprends. Vous souhaitez annoncer un retard"
    elif intent == Intents.QUESTION:
        intent_script = "Parfait. Vous souhaitez poser une question"
    elif intent == Intents.URGENCE:
        intent_script = "Je comprends que vous souhaitez signaler une urgence"
    else:
        intent_script = f"Vous souhaitez {intent.value}"

    return intent_script


def estimate_tts_duration(text, wpm=150):
    words = text.split()
    num_words = len(words)
    duration_seconds = (num_words / wpm) * 60
    return duration_seconds


def get_appointment_description(appointment: AppointmentForm):
    time_str = appointment.start_date.strftime("%H:%M")
    return f"rendez-vous pour {appointment.visit_motive_name} à {time_str}"
