from datetime import datetime, timedelta
from constants import DAYS_OF_THE_WEEK
from typing import List, Literal
from models import Appointment
from num2words import num2words
from models import Appoint<PERSON><PERSON><PERSON>, <PERSON>, PatientData, CompanyData
from rapidfuzz import fuzz
from loguru import logger
from zoneinfo import ZoneInfo


def get_different_time_slots(
    time_slots: List[Appointment], min_time_difference: int, number_of_time_slots: int
):
    """
    Args:
        time_slots: list of time slots available
        min_time_difference: int representing the minimum time difference in hours between two time slots.
        number_of_time_slots: int representing the number of time slots to return.
    """
    suggested_times: List[Appointment] = []
    min_gap = timedelta(hours=min_time_difference)

    for i, time_slot in enumerate(time_slots):
        start_time = time_slot.start_date

        if not suggested_times:
            suggested_times.append(time_slot)
            continue

        last_end_time = suggested_times[-1].end_date

        if start_time - last_end_time >= min_gap:
            suggested_times.append(time_slot)

        if len(suggested_times) == number_of_time_slots:
            break

    return suggested_times


def get_date_from_day(day_name: str, after_week: int = 0) -> datetime:
    """Returns the date of the next day (lundi, mardi, etc.)"""
    try:
        day = DAYS_OF_THE_WEEK[day_name.lower()].value
        if day is None:
            logger.error(f"Invalid day name provided: {day_name}")
            return None

        today = datetime.now()
        days_ahead = day - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7

        if after_week > 0:
            days_ahead += after_week * 7

        return today + timedelta(days_ahead)
    except Exception as e:
        logger.error(f"Error getting date from day: {day_name}. Error: {e}")
        return None


def get_day_from_date(date: datetime):
    """Returns the day of the week in French (lundi, mardi, etc.)"""
    return DAYS_OF_THE_WEEK[date.strftime("%A").lower()].name.capitalize()


days_of_the_week_eng_to_fr = {
    "Monday": "Lundi",
    "Tuesday": "Mardi",
    "Wednesday": "Mercredi",
    "Thursday": "Jeudi",
    "Friday": "Vendredi",
    "Saturday": "Samedi",
    "Sunday": "Dimanche",
}
months_eng_to_fr = {
    "January": "Janvier",
    "February": "Février",
    "March": "Mars",
    "April": "Avril",
    "May": "Mai",
    "June": "Juin",
    "July": "Juillet",
    "August": "Août",
    "September": "Septembre",
    "October": "Octobre",
    "November": "Novembre",
    "December": "Décembre",
}


def format_datetime_with_day_in_french(date: datetime):
    """
    Args:
        date: datetime object
    Returns:
        str: full date in French (e.g. Le Lundi 1 Janvier 2021 à 14 heure 00)
    """
    day = days_of_the_week_eng_to_fr[date.strftime("%A")]
    heure_str = num2words(date.hour, lang="fr")
    minute_str = num2words(date.minute, lang="fr") if date.minute != 0 else ""
    year = date.year
    heure_part = f"à {heure_str} heure" if date.hour != 0 and date.minute != 59 else ""
    minute_part = f"{minute_str}" if date.minute != 0 and date.minute != 59 else ""
    time_parts = " ".join(part for part in [heure_part, minute_part] if part)
    return f" {day} {date.day} {months_eng_to_fr[date.strftime('%B')]} {time_parts} (dit en toute lettre, utilise bien le jour {day}. Pour rappel, l'année est {year} mais tu n'as pas besoin de la dire)"


def format_appointment_to_datetime_with_day_in_french(appointment: Appointment):
    """
    Args:
        appointment: Appointment object
    Returns:
        str: full date in French (e.g. Le Lundi 1 Janvier 2021 à 14 heure 00 avec docteur X)
    """
    # Vérifier si c'est un rendez-vous avec plusieurs examens
    if appointment.steps and len(appointment.steps) > 1:
        # Formatter les rendez-vous multiples
        steps_str = []
        for idx, step in enumerate(appointment.steps):
            step_date = step.get("start_date")
            if isinstance(step_date, str):
                step_date = datetime.fromisoformat(step_date)

            day = days_of_the_week_eng_to_fr[step_date.strftime("%A")]
            heure_str = num2words(step_date.hour, lang="fr")
            minute_str = (
                num2words(step_date.minute, lang="fr") if step_date.minute != 0 else ""
            )

            heure_part = f"à {heure_str} heure" if step_date.hour != 0 else ""
            minute_part = f"{minute_str}" if step_date.minute != 0 else ""
            time_parts = " ".join(part for part in [heure_part, minute_part] if part)

            # Pour le premier examen
            if idx == 0:
                steps_str.append(f"le premier examen {time_parts}")
            else:
                steps_str.append(f"le second examen {time_parts}")

        year = appointment.start_date.year
        day = days_of_the_week_eng_to_fr[appointment.start_date.strftime("%A")]
        date_str = f"{day} {appointment.start_date.day} {months_eng_to_fr[appointment.start_date.strftime('%B')]}"

        return f"{date_str} avec {' et '.join(steps_str)} (dit en toute lettre, utilise bien le jour {day}. Pour rappel, l'année est {year} mais tu n'as pas besoin de la dire)"

    # Comportement original pour un seul examen
    day = days_of_the_week_eng_to_fr[appointment.start_date.strftime("%A")]
    heure_str = num2words(appointment.start_date.hour, lang="fr")
    minute_str = (
        num2words(appointment.start_date.minute, lang="fr")
        if appointment.start_date.minute != 0
        else ""
    )
    year = appointment.start_date.year
    medecin = (
        "avec docteur " + appointment.medecin.split("(")[0].strip()
        if len(appointment.medecin.split(" ")) > 1
        and "radiolog" not in appointment.medecin.lower()
        else ""
    )
    heure_part = f"à {heure_str} heure" if appointment.start_date.hour != 0 else ""
    minute_part = f"{minute_str}" if appointment.start_date.minute != 0 else ""
    time_parts = " ".join(part for part in [heure_part, minute_part] if part)

    return f" {day} {appointment.start_date.day} {months_eng_to_fr[appointment.start_date.strftime('%B')]} {time_parts} {medecin} (dit en toute lettre, utilise bien le jour {day}. Pour rappel, l'année est {year} mais tu n'as pas besoin de la dire)"


def get_time_slots_for_date(
    time_slots: List[Appointment], date: datetime
) -> List[Appointment]:
    """
    Args:
        time_slots: list of time slots available
        day: str representing the day of the week in French (lundi, mardi, etc.)
    """
    time_slots_for_date = []
    for time_slot in time_slots:
        if time_slot.start_date.date() == date.date():
            time_slots_for_date.append(time_slot)
    return time_slots_for_date


def is_valid_datetime(datetime_str: str, format: str):
    if datetime_str is None or datetime_str == "":
        logger.error("Datetime string is None")
        return False

    if isinstance(datetime_str, datetime):
        return True
    try:
        datetime.strptime(datetime_str, format)
        return True
    except ValueError:
        return False
    except Exception as e:
        logger.error(f"Error parsing datetime: {e}")
        return False


def is_valid_date(date_str: str):
    if isinstance(date_str, datetime):
        logger.warning("Already a datetime object")
        return True
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def calculate_age(birth_date: datetime, today: datetime = None) -> int:
    if isinstance(birth_date, str):
        birth_date = datetime.strptime(birth_date, "%Y-%m-%d")
        logger.warning("String is given")

    if today is None:
        today = datetime.now()

    age = (
        today.year
        - birth_date.year
        - ((today.month, today.day) < (birth_date.month, birth_date.day))
    )
    return age


def is_minimum_age(birth_date: datetime, minimum_age: float) -> bool:
    if not minimum_age:
        return True
    return calculate_age(birth_date) >= minimum_age


def is_maximum_age(birth_date: datetime, maximum_age: float) -> bool:
    if not maximum_age:
        return True
    return calculate_age(birth_date) <= maximum_age


def is_appointment_has_passed(appointment: AppointmentForm):
    if not appointment or not appointment.id:
        return True

    if not isinstance(appointment.start_date, datetime):
        logger.error(
            f"Appointment start date is not a datetime object: {appointment.start_date}"
        )
        return True

    now = datetime.now(appointment.start_date.tzinfo)
    return appointment.start_date < now


def is_appointment_soon(appointment: AppointmentForm, minutes: int):
    if not appointment or not appointment.id:
        return False

    if not isinstance(appointment.start_date, datetime):
        logger.error(
            f"Appointment start date is not a datetime object: {appointment.start_date}"
        )
        return False

    now = datetime.now(appointment.start_date.tzinfo)
    return appointment.start_date - timedelta(minutes=minutes) < now


def normalize_name(name: str) -> str:
    return name.split("(")[0].strip().lower()


def get_doctor_by_name(name: str, doctors: List["Doctor"]):
    doctor = None
    max_ratio = 0
    input_name = normalize_name(name)

    for _doc in doctors:
        doc_name = normalize_name(_doc.name)
        # Try normal and swapped comparisons
        ratio1 = fuzz.ratio(doc_name, input_name)
        ratio2 = fuzz.ratio(" ".join(reversed(doc_name.split())), input_name)
        best_ratio = max(ratio1, ratio2)

        if best_ratio > max_ratio:
            max_ratio = best_ratio
            doctor = _doc

    return doctor


def get_doctor_by_agenda_id(agenda_id: str, doctors: List[Doctor]):
    """
    Args:
        agenda_id: str representing the agenda ID of the doctor
        doctors: list of Doctor objects
    Returns:
        Doctor object if found, None otherwise
    """
    for doctor in doctors:
        if str(doctor.agenda_id) == str(agenda_id):
            return doctor

    return None


def get_closest_appointments(
    appointments: List[Appointment],
    datetime_wished: datetime,
    on_same_day: bool = True,
    max_difference_hours: int = 2,
    blacklist_appointment: List[Appointment] = [],
) -> List[Appointment]:
    """
    Args:
        appointments: list of Appointment objects
        datetime_wished: datetime object representing the wished appointment date
        on_same_day: if True, only consider appointments on the same calendar day
        max_difference_hours: maximum time difference in hours to consider an appointment
    Returns:
        List of up to 2 Appointment objects that are closest to the wished datetime
    """

    def make_aware(dt: datetime, default_tz="Europe/Paris") -> datetime:
        if dt.tzinfo is not None and dt.tzinfo.utcoffset(dt) is not None:
            return dt
        return dt.replace(tzinfo=ZoneInfo(default_tz))

    datetime_wished = make_aware(datetime_wished)
    max_diff_seconds = max_difference_hours * 3600

    filtered_appointments: List[Appointment] = []
    for appointment in appointments:
        if appointment in blacklist_appointment:
            continue
        appointment_time = make_aware(appointment.start_date)
        difference = abs((appointment_time - datetime_wished).total_seconds())

        if difference <= max_diff_seconds:
            if on_same_day:
                if appointment_time.date() == datetime_wished.date():
                    if difference == 0:
                        return [appointment]
                    filtered_appointments.append(appointment)
            else:
                filtered_appointments.append(appointment)

    if not filtered_appointments:
        return []

    # Sort by time difference
    filtered_appointments.sort(
        key=lambda x: abs(
            (
                x.start_date.replace(tzinfo=ZoneInfo("Europe/Paris")) - datetime_wished
            ).total_seconds()
        )
    )
    seen = set()
    unique_appointments = []

    for appointment in filtered_appointments:
        if appointment.start_date not in seen:
            seen.add(appointment.start_date)
            unique_appointments.append(appointment)

    return unique_appointments[:2]


def get_appointments_on_day(
    appointments: List[Appointment],
    wish_appointment_date: datetime,
    blacklist_appointment: List[Appointment] = [],
) -> List[Appointment]:
    """
    Args:
        appointments: list of Appointment objects
        appointment_date: datetime object representing the date to filter appointments
    Returns:
        List of Appointment objects that match the given date
    """

    appointment_date = wish_appointment_date.date()
    return [
        appointment
        for appointment in appointments
        if appointment.start_date.date() == appointment_date
        and appointment not in blacklist_appointment
    ]


def get_appointments_in_half_day(
    appointments: List[Appointment],
    appointment_datetime: datetime,
    blacklist_appointment: List[Appointment] = [],
) -> List[Appointment]:
    """
    Args:
        appointments: list of Appointment objects
        appointment_date: datetime object representing the date to filter appointments
    Returns:
        List of Appointment objects that match the given date and are in the morning or afternoon
    """
    appointment_date = appointment_datetime.date()
    is_morning = appointment_datetime.hour < 12

    return [
        appointment
        for appointment in appointments
        if appointment.start_date.date() == appointment_date
        and (
            (is_morning and appointment.start_date.hour < 12)
            or (not is_morning and appointment.start_date.hour >= 12)
        )
        and appointment not in blacklist_appointment
    ]


def is_patient_blocked_due_to_no_show(
    patient_data: PatientData, company_data: CompanyData
) -> tuple[bool, str | None]:

    if company_data.config == "config119" and patient_data.num_no_show > 3:
        return True
    elif company_data.config == "config87" and patient_data.num_no_show > 3:
        return True
    elif company_data.config in ["config88", "config108"]:
        if patient_data.bounced_at:
            return True

    return False
