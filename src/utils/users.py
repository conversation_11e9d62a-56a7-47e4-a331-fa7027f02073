from models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oint<PERSON><PERSON><PERSON>, Doctor, CompanyData
from datetime import datetime
from constants import GENDER


def patient_json_to_patient_data(
    patient_json: dict,
    company_data: CompanyData,
) -> PatientData:
    last_appointment_data = patient_json.get("last_appointment")
    next_appointment_data = patient_json.get("next_appointment")

    patient_data = PatientData(is_new_patient=False)
    for key, value in patient_json.items():
        if key in patient_data.__dict__:
            if key == "appointments":
                patient_data.appointments = [
                    AppointmentForm(config=company_data.config, **appointment)
                    for appointment in value
                ]
                continue
            if key == "birthdate" and value:
                value = datetime.strptime(value, "%Y-%m-%d")
            if key == "gender" and (isinstance(value, bool) or value is None):
                value = (
                    GENDER.FEMALE
                    if value
                    else GENDER.MALE if value is not None else GENDER.UNDEFINED
                )

            setattr(patient_data, key, value)

    if last_appointment_data:
        patient_data.last_appointment = AppointmentForm(
            config=company_data.config, **patient_data.last_appointment
        )

    if next_appointment_data:
        patient_data.next_appointment = AppointmentForm(
            config=company_data.config, **patient_data.next_appointment
        )

    if patient_json.get("medecin_historique"):
        patient_data.medecin_historique = Doctor(
            **patient_json.get("medecin_historique")
        )
    if patient_json.get("historical_doctors"):
        patient_data.historical_doctors = {}
        for speciality_id, doctor in patient_json.get("historical_doctors").items():
            doctor_obj = Doctor(**doctor)
            speciality_id = int(speciality_id)
            patient_data.historical_doctors[speciality_id] = doctor_obj

    return patient_data
