#! fastapi dev

import os
import asyncio
import aiohttp
import argparse
import webbrowser
import time

from loguru import logger

from lib.n8n_client import n8n_client

from constants import (
    REQUIRED_ENV_VARS,
    DAILY_API_KEY,
    DAILY_API_URL,
    MAX_SESSION_TIME,
    NODE_ENV,
)

from lib.daily_client import DailyClient
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import traceback


# Import routers
from routers import (
    health_router,
    status_router,
    daily_router,
    twilio_router,
    browser_router,
    svi_router,
    generate_router,
)


def get_config():
    parser = argparse.ArgumentParser(description="Pipecat Bot Runner")
    parser.add_argument(
        "--host", type=str, default=os.getenv("HOST", "0.0.0.0"), help="Host address"
    )
    parser.add_argument(
        "--port", type=int, default=os.getenv("PORT", 7860), help="Port number"
    )
    parser.add_argument(
        "--reload", action="store_true", default=False, help="Reload code on change"
    )
    parser.add_argument(
        "-n", "--client_name", type=str, help="Client name", default=None
    )
    parser.add_argument("--phone", type=str, help="Phone number", default=None)
    parser.add_argument("-c", "--config_id", type=str, help="Config ID", default=None)

    # if the option (no need for a value) is present, it will be set to True
    parser.add_argument(
        "--browser",
        action="store_true",
        default=False,
        help="Open the browser to start the bot",
    )
    parser.add_argument("--exp", type=int, default=None, help="Session expiration time")
    parser.add_argument(
        "--test",
        nargs="?",
        const=True,
        default=False,
        help="Run the test suite (optionally provide an ID)",
    )

    parser.add_argument(
        "--speed_voice",
        type=str,
        default="1.15",
        help="Speed of the voice in the bot",
    )

    return parser.parse_args()


# Example background task cleanup function
async def cleanup():
    # Simulate waiting for processes to finish
    start_time = asyncio.get_event_loop().time()
    while len(app.state.running_clients.keys()) > 0 and NODE_ENV != "development":
        # Ensure we're not waiting longer than the allowed time
        if asyncio.get_event_loop().time() - start_time > MAX_SESSION_TIME:
            print("Max session time reached, forcing cleanup...")
            break

        print(f"Waiting for {len(app.state.running_clients)} clients to finish...")
        await asyncio.sleep(1)
        now_time = time.time()
        # remove all clients that have been running for more than MAX_SESSION_TIME
        for call_id, bot_start_time in list(app.state.running_clients.items()):
            if now_time - bot_start_time > MAX_SESSION_TIME:
                del app.state.running_clients[call_id]
                logger.info(f"Removed client {call_id} from running clients")

    print("Cleanup completed.")


@asynccontextmanager
async def lifespan(app):
    config = get_config()
    async with aiohttp.ClientSession() as session:
        app.state.running_clients = {}
        app.state.daily_client = DailyClient(
            daily_api_key=DAILY_API_KEY,
            daily_api_url=DAILY_API_URL,
            aiohttp_session=session,
        )

        if config.browser:
            base_url = f"http://{os.getenv('HOST', 'localhost')}:{os.getenv('PORT', 7860)}/browser/start_bot"
            app.state.client_name = config.client_name
            app.state.config_id = (
                f"config{config.config_id}"
                if config.config_id and "config" not in config.config_id
                else config.config_id
            )
            client_name = app.state.client_name
            config_id = app.state.config_id
            room_url = f"{base_url}/{client_name}/{config_id}"

            logger.info(f"Enter {client_name} Room URL: {room_url}")
            webbrowser.open_new_tab(room_url)
        else:
            logger.info("Waiting for an endpoint to be called...")

        if config.client_name and config.config_id:
            app.state.client_name = config.client_name
            app.state.config_id = (
                f"config{config.config_id}"
                if config.config_id and "config" not in config.config_id
                else config.config_id
            )
            logger.info(f"Customer: {app.state.client_name}")
            logger.info(f"Config ID: {app.state.config_id}")

        yield

        await cleanup()
        await session.close()


app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router)
app.include_router(generate_router)
app.include_router(status_router)
app.include_router(daily_router)
app.include_router(twilio_router)
app.include_router(browser_router)
app.include_router(svi_router)


if __name__ == "__main__":
    config = get_config()

    # Check environment variables
    for env_var in REQUIRED_ENV_VARS:
        if env_var not in os.environ:
            raise Exception(f"Missing environment variable: {env_var}.")

    try:
        import uvicorn

        uvicorn.run(
            "main:app", host=config.host, port=config.port, reload=config.reload
        )
    except ImportError:
        n8n_client.send_error_server(config.config_id, None, traceback.format_exc())
