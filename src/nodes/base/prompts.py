from typing import Optional, Any
from models import CompanyData, PatientData
from constants import Intents
import utils.appointments
from .tools import BaseTools
import json


class BasePrompts:
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data

    def _set_message(self, message: str):
        message = " ".join(
            [line.strip() for line in message.split("\n") if line.strip()]
        )
        return {"role": "system", "content": message}

    def init(self, all_in_task: bool = False):
        list_intents = ", ".join([intent.value for intent in Intents])

        msg_cannot_tranfer = (
            (
                "Si le patient souhaite être transféré, tu dois dire au patient que tu ne peux pas le transférer mais que tu vas laisser un message au secrétariat."
            )
            if all_in_task or self.company_data.bot_configuration.all_in_task
            else ""
        )

        message = self._set_message(
            f"""
                Tu dis:  'Bienvenue au {self.company_data.name}. Comment puis-je vous aider ?'
                -- 
                Informations complémentaires si tu peux répondre à la question et appeler aucune fonction:
                Adresse: {self.company_data.address}
                Heure ouvertures: {self.company_data.openings}
                Information complémentaire: {self.company_data.additional_information}
                ---
                Si tu as compris une des raison de l'appel, utilise la fonction 'handle_check_intent' avec une de ces valeurs {list_intents} pour intent
                Sinon, excuse toi et redemande la raion de l'appel.
                Si l'utilisateur dit juste "rendez-vous", utilise la fonction 'handle_check_intent' avec la valeur 'nouveau'.
                Si l'utilisateur dit "renouvellement", utilise la fonction 'handle_check_intent' avec la valeur 'nouveau'.
                Si l'utilisateur dit 'modifier', utilise la fonction 'handle_check_intent' avec la valeur 'modifier'.
                Si l'utilisateur dit 'retard', utilise la fonction 'handle_check_intent' avec la valeur 'retard'.
                Si l'utilisateur dit 'contacter' ou 'parler au secrétariat', utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit 'question', utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit 'resultat', utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit 'ordonannce' utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit 'annuler' utilise la fonction 'handle_check_intent' avec la valeur 'annuler'
                Si l'utilisateur dit que le centre a essayé de la contacter, utilise la fonction 'forward_call'.
                Si l'utilisateur veut savoir quand il est son prochain rendez-vous, utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur veut être transféré, utilise la fonction 'forward_call'.
                {msg_cannot_tranfer}
                Si tu ne comprends pas la raison de l'appel, demande lui poliment la raison de l'appel.
                
          '"""
        )

        tools = [BaseTools.handle_check_intent, BaseTools.forward_call]

        return message, tools

    def ask_confirm_identity(
        self, patient_data: PatientData, return_node: Optional[dict[str, Any]] = None
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
              🚨 Ta tâche maintenant est de dire **exactement et uniquement** cette phrase, sans modification, sans ajout, sans reformulation :
              👉 'Afin de poursuivre j'ai besoin de confirmer votre identité. Est-ce que votre appel est pour {patient_data.first_name} {patient_data.last_name} ?'
              ---
              Si dit oui ou non, utilise la fonction handle_confirm_identity. True si il dit oui False si il dit non.
              Il peut seulement dire 'oui' ou 'non'.
              {"Si le souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
              Il doit répondre à ta question.
              """
        )

        tools = [
            BaseTools.handle_confirm_identity,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_to_tranfer_call(self):
        message = self._set_message(
            """
            Dit: Merci pour votre appel. Souhaitez-vous être transféré au centre d'appel? ou souhaitez-vous terminer l'appel?
            --
            Si dit oui ou transfert, utilise la fonction forward_call
            Si dit non ou terminer, utilise la fonction end_call
            """
        )

        tools = [BaseTools.forward_call, BaseTools.end_call]

        return message, tools

    def ask_question(
        self,
        patient_data: PatientData = None,
    ):
        opening_hours = self.company_data.openings
        address = self.company_data.address
        additional_info = self.company_data.additional_information
        if not opening_hours:
            opening_hours = json.dumps(self.company_data.openings_2)

        next_appointments = ""
        if not patient_data:
            patient_information = """
              Tu n'as pas d'informations sur le patient, donc tu ne peux pas lui donner d'informations sur ses prochains rendez-vous.
              Si le patient a une question médicale, tu n'as pas le droit de répondre. Dis simplement :
              "Je ne suis pas habilité à répondre à des questions médicales." Puis pose 1-2 questions pour avoir plus d'informations sur la question médicale.
              
            """
        else:
            if patient_data.appointments:
                next_appointments = "\n - ".join(
                    [
                        utils.appointments.format_datetime_with_day_in_french(
                            appointment.start_date
                        )
                        for appointment in patient_data.appointments
                    ]
                )
            else:
                next_appointments = "Aucun rendez-vous prévu"

            patient_information = (
                next_appointments
                and f"""
                    Information sur le patient:
                    
                    - prochains rendez-vous: {next_appointments}
                  """
            )

        message = self._set_message(
            f"""
              Dit: "Quelle est votre question ?" {"Après Demande son nom et prénom" if not patient_data.is_identify() else ""}"
              
              
              Knowledge base:
              - Horaires d'ouverture: {opening_hours}
              
              - Adresse: {address}
              
              - Informations supplémentaires: {additional_info}
              
              !Attention!
              Question médicale :
              Si le patient a une question médicale, tu n'as pas le droit de répondre. Dis simplement :
              "Je ne suis pas habilité à répondre à des questions médicales." Puis pose 1-2 questions pour avoir plus d'informations sur la question médicale.
              S'il ne veut pas ajouter d'autres informations, dis lui au revoir.
              Si le patient souhaite une ordonnance ou résultat d'examen, dis lui que tu vas transmettre sa demande au secrétariat puis demande s'il souhaite ajouter quelque chose.
             

              exemple de conversation :
              Réceptionniste : "Pouvez-vous m'expliquer votre question ou votre problème ?"
              Patient : "J'ai un problème de facturation."
              Réceptionniste : "Pouvez-vous m'en dire un peu plus sur votre problème de facturation afin que je puisse vous aider ?"
              Patient : "Voici mon problème : [exemple de problème]."

              À ce moment-là, réponds à son problème si les informations nécessaires sont disponibles dans le prompt.

              Si tu ne connais pas la réponse, ne l'invente pas et dis simplement :
              "J'ai bien compris votre problème et je peux laisser une note au secrétariat." Si tu veux demande 1-2 questions pour avoir plus d'informations sur le problème.

              Résultats :
                  •	Délais : Si des résultats doivent être transmis, ils seront envoyés sous 24 à 48 heures après le rendez-vous.
                  •	Consultation des résultats : Les documents sont disponibles sur votre fiche diffusion remise à la facturation. Un mail avec vos identifiants a également été envoyé (vérifiez dans vos spams). Informations supplémentaires sur le centre : + {additional_info} 
              
              
              {patient_information}
              """
        )

        tools = []

        return message, tools

    def ask_last_question(
        self,
        return_node: Optional[dict[str, Any]] = None,
        sentence: Optional[str] = None,
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""
        sentence = sentence or "Souhaitez-vous laisser un message au secrétariat?"

        message = self._set_message(
            f"""
            Dit: {sentence}
            --
            {"Si le souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
            utilise la fonction 'handle_last_question' avec True si le client a une question ou un message à laisser, False sinon.
            
            Si le client souhaite avoir des informations que tu as déjà eu dans la conversation, réponds lui avec les informations que tu as déjà eu.
            """
        )
        tools = [BaseTools.handle_last_question]
        tools.append(return_node) if return_node else None
        return message, tools
