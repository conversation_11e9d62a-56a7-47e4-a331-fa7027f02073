from abc import ABC
from typing import Dict, List, Any, Optional
from constants import Intents


class BaseTools(ABC):
    @staticmethod
    def create_tool_description(
        name: str, description: str, properties: Optional[List[Dict[str, Any]]] = None
    ) -> dict[str, Any]:
        properties_dict = {}
        required_fields = []

        if properties:
            properties_dict = {
                prop["name"]: {"type": prop["type"], "description": prop["description"]}
                for prop in properties
            }
            required_fields = [
                prop["name"] for prop in properties if prop.get("required")
            ]

        return {
            "type": "function",
            "function": {
                "name": name,
                "description": description,
                "parameters": (
                    {
                        "type": "object",
                        "properties": properties_dict,
                        "required": required_fields,
                    }
                    if properties
                    else {}
                ),
            },
        }

    handle_check_intent = create_tool_description(
        name="handle_check_intent",
        description="Check Intent of the call",
        properties=[
            {
                "name": "intent",
                "type": "string",
                "description": "The intent of the call",
                "required": True,
                "enum": [intent.value for intent in Intents],
            },
            {
                "name": "appointment_motive",
                "type": "string",
                "description": "Le motif du rendez-vous si l'intention est de prendre un rendez-vous. (ex: 'Consultation générale', 'radio du <partie du corps>', 'prise de sang', 'vaccination', etc.). Laisser vide si pas mentionné.",
                "required": False,
            },
            {
                "name": "doctor_name",
                "type": "string",
                "description": "Le nom du médecin mentionné par le patient",
                "required": False,
            },
        ],
    )

    forward_call = create_tool_description(
        name="forward_call",
        description="Utilise cette fonction pour transferer l'appel",
    )

    end_call = create_tool_description(
        name="end_call",
        description="Utilise cette fonction pour terminer l'appel",
    )

    handle_last_question = create_tool_description(
        name="handle_last_question",
        description="Utilise cette fonction pour savoir si le patient a une dernière question",
        properties=[
            {
                "name": "has_last_question",
                "type": "boolean",
                "description": "True si le patient a une dernière question sinon False",
                "required": True,
            }
        ],
    )

    handle_confirm_identity = create_tool_description(
        name="handle_confirm_identity",
        description="Utilise cette fonction pour confirmer l'identité du client",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "La confirmation de l'identité du client si c'est pour quelqu'un d'autre la valeur est False",
                "required": True,
                "enum": [True, False],
            }
        ],
    )

    handle_question = create_tool_description(
        name="handle_question",
        description="Utilise cette fonction pour répondre à une question",
        properties=[
            {
                "name": "answer",
                "type": "string",
                "description": "La réponse à la question",
                "required": True,
            }
        ],
    )
