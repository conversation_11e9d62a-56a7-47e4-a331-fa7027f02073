from ..base.tools import BaseTools

create_tool_description = BaseTools.create_tool_description


class ContactFormTools(BaseTools):

    handle_first_name = create_tool_description(
        name="handle_first_name",
        description="Utilise cette fonction pour confirmer le prénom du client",
        properties=[
            {
                "name": "first_name",
                "type": "string",
                "description": "Le prénom du client",
                "required": True,
            }
        ],
    )

    handle_new_or_existing_patient = create_tool_description(
        name="handle_new_or_existing_patient",
        description="Utilise cette fonction pour confirmer si le client est un nouveau patient ou non",
        properties=[
            {
                "name": "is_new_patient",
                "type": "boolean",
                "description": "Le client est-il un nouveau patient ?",
                "required": True,
            }
        ],
    )

    handle_last_name = create_tool_description(
        name="handle_last_name",
        description="Utilise cette fonction pour confirmer le nom du client",
        properties=[
            {
                "name": "last_name",
                "type": "string",
                "description": "Le nom du client",
                "required": True,
            }
        ],
    )

    handle_birthdate_contact_form = create_tool_description(
        name="handle_birthdate_contact_form",
        description="Utilise cette fonction lorsque tu as compris la date de naissance du patient",
        properties=[
            {
                "name": "birthdate",
                "type": "string",
                "description": "La date de naissance du patient en format YYYY-MM-DD.",
                "required": True,
            }
        ],
    )

    handle_identity_confirmation_contact_form = create_tool_description(
        name="handle_identity_confirmation_contact_form",
        description="Utilise cette fonction pour indiquer si le patient confirme ou non être la personne concernée.",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "Mettre `True` si le patient affirme que c'est bien lui, sinon `False`.",
                "required": True,
                "enum": [True, False],
            }
        ],
    )
