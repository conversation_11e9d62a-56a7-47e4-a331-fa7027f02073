from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from nodes.customer_base.intake_processor import ContactFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from rapidfuzz import fuzz
from loguru import logger
from .handle_new_or_existing_patient import (
    _search_existing_patients,
    _calculate_patient_match_score,
)
from nodes.contact_form.tools import ContactFormTools


async def handle_first_name(
    self: "ContactFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):

    first_name = str(args["first_name"]).strip()
    self._patient_data.first_name = first_name

    if first_name and not self._patient_data.is_new_patient:
        # Use unified search method

        await self._say_and_wait(
            llm,
            context,
            f"Merci {first_name}, un instant, je vérifie si je vous trouve dans notre système. ",
            no_wait=True,
        )

        possible_patients = await _search_existing_patients(
            self,
            first_name=self._patient_data.first_name,
            last_name=self._patient_data.last_name,
            birthdate=self._patient_data.birthdate,
        )

        # Find best matching patient using comprehensive scoring
        best_patient = None
        best_score = 0

        for patient in possible_patients:
            if not patient.first_name or not patient.last_name:
                logger.warning(
                    f"Patient {patient} does not have a first name or last name."
                )
                continue

            score = _calculate_patient_match_score(
                patient,
                first_name,
                self._patient_data.last_name,
                self._patient_data.birthdate,
            )

            # Require minimum 60% match score (allows good name matches even with completely wrong birthdate)
            if score > 60 and score > best_score:
                best_patient = patient
                best_score = score
                logger.debug(
                    f"Patient candidate: {patient.first_name} {patient.last_name} "
                    f"(birthdate: {getattr(patient, 'birthdate', 'N/A')}) - Score: {score:.1f}%"
                )

        if best_patient:
            self.possible_patient = best_patient
            birthdate_msg = ""
            if self._patient_data.birthdate and hasattr(best_patient, "birthdate"):
                if str(best_patient.birthdate) != str(self._patient_data.birthdate):
                    birthdate_msg = " (Note: birthdate differs)"

            logger.info(
                f"Found matching patient: {best_patient.first_name} {best_patient.last_name} "
                f"with score: {best_score:.1f}%{birthdate_msg}"
            )
            prompt, tools = self.prompts.ask_identity_contact_form(best_patient)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    if first_name:
        self._patient_data.first_name = first_name
        return await self.output_function(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
        )

    await self._reply_to_user(
        result_callback,
        context,
        llm,
        "Désolé, je n'ai pas compris votre prénom. Pouvez-vous répéter?",
        [ContactFormTools.handle_first_name],
    )
    return
