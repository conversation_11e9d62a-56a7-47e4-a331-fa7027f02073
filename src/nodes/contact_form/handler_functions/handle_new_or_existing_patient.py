from typing import TYPE_CHECKING, List, Optional
from rapidfuzz import fuzz
from models import PatientData

if TYPE_CHECKING:
    from contact_form.intake_processor import ContactFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)


async def handle_new_or_existing_patient(
    self: "ContactFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    is_new_patient = args["is_new_patient"]
    is_new_patient = is_new_patient == True or "oui" in str(is_new_patient).lower()
    self.has_already_asked_if_new_patient = True

    self._patient_data.is_new_patient = is_new_patient

    if not is_new_patient:
        self._patient_data.is_new_patient = False
        prompt, tools = self.prompts.ask_birthdate_contact_form()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    prompt, tools = self.prompts.ask_last_name()
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return


async def _search_existing_patients(
    self: "ContactFormIntakeProcessor",
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    birthdate: Optional[str] = None,
) -> List:
    """Unified patient search with progressive fallback strategy"""
    search_strategies = [
        # Most specific: all three parameters
        (first_name, last_name, birthdate),
        # Two parameters combinations - prioritize name combinations without birthdate
        (first_name, last_name, None),  # This is key for wrong birthdate scenarios
        (first_name, None, birthdate),
        (None, last_name, birthdate),
        # Single parameter fallbacks
        (first_name, None, None),
        (None, last_name, None),
    ]

    all_patients = []
    for fn, ln, bd in search_strategies:
        if not fn and not ln and not bd:
            continue

        if (
            (fn is None and ln is None)
            or (fn is None and bd is None)
            or (ln is None and bd is None)
        ):
            continue

        patients = await self.booking_provider.get_patient_information_by_center(
            "", fn, ln, bd
        )
        print(f"Searching for patients with: {fn}, {ln}, {bd}")
        if patients:
            all_patients.extend(patients)

    # Remove duplicates based on patient ID or unique identifier
    seen_patients = set()
    unique_patients = []
    for patient in all_patients:
        patient_id = (
            getattr(patient, "id", None)
            or f"{patient.first_name}_{patient.last_name}_{getattr(patient, 'birthdate', 'unknown')}"
        )
        if patient_id not in seen_patients:
            seen_patients.add(patient_id)
            unique_patients.append(patient)

    return unique_patients


def _calculate_patient_match_score(
    patient: PatientData,
    target_first_name: str,
    target_last_name: Optional[str] = None,
    target_birthdate: Optional[str] = None,
) -> float:
    """Calculate comprehensive matching score for a patient - handles wrong birthdates gracefully"""
    score = 0

    # First name matching (weight: 50% - high importance)
    first_name_score = 0
    if patient.first_name and target_first_name:
        first_name_score = fuzz.ratio(
            target_first_name.lower().strip(),
            patient.first_name.lower().strip(),
        )
        score += first_name_score * 0.5

    # Last name matching (weight: 40% - high importance)
    last_name_score = 0
    if patient.last_name and target_last_name:
        last_name_score = fuzz.ratio(
            target_last_name.lower().strip(),
            patient.last_name.lower().strip(),
        )
        score += last_name_score * 0.4

    # Birthdate matching (weight: 10% - low penalty for wrong birthdate)
    birthdate_score = 0
    if patient.birthdate and target_birthdate:
        if str(patient.birthdate) == str(target_birthdate):
            birthdate_score = 100  # Perfect match
        else:
            # Even with wrong birthdate, don't heavily penalize if names match well
            birthdate_score = 20  # Small bonus for having birthdate data, even if wrong
        score += birthdate_score * 0.1
    elif not target_birthdate or not patient.birthdate:
        # No penalty for missing birthdate data
        birthdate_score = 50
        score += birthdate_score * 0.1

    return score
