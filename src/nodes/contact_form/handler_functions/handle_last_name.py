from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from contact_form.intake_processor import ContactFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from nodes.contact_form.tools import ContactFormTools
from rapidfuzz import fuzz
from loguru import logger
from .handle_new_or_existing_patient import (
    _search_existing_patients,
    _calculate_patient_match_score,
)


async def handle_last_name(
    self: "ContactFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):

    last_name = str(args["last_name"]).strip()
    self._patient_data.last_name = last_name

    if last_name and not self._patient_data.is_new_patient:
        # Use unified search method

        possible_patients = await _search_existing_patients(
            self,
            first_name=self._patient_data.first_name,
            last_name=self._patient_data.last_name,
            birthdate=self._patient_data.birthdate,
        )

        # Store all possible patients for later use
        if not hasattr(self, "possible_patients"):
            self.possible_patients = []
        self.possible_patients.extend(possible_patients)

        # Find best matching patient
        best_patient = None
        best_score = 0

        for patient in possible_patients:
            if not patient.last_name:
                continue

            score = _calculate_patient_match_score(
                patient,
                self._patient_data.first_name or "",
                last_name,
                self._patient_data.birthdate,
            )

            # Require minimum 75% match score
            if score > 75 and score > best_score:
                best_patient = patient
                best_score = score

        if best_patient:
            self.possible_patient = best_patient
            logger.info(f"Found matching patient with score: {best_score:.1f}%")
            prompt, tools = self.prompts.ask_identity_contact_form(best_patient)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    if last_name:
        prompt, tools = self.prompts.ask_first_name()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    await self._reply_to_user(
        result_callback,
        context,
        llm,
        "Désolé, je n'ai pas compris votre nom. Pouvez-vous répéter?",
        [ContactFormTools.handle_last_name],
    )
    return
