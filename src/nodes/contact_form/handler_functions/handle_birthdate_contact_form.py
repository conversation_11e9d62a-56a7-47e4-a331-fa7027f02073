from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from contact_form.intake_processor import ContactFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

import utils.appointments
from datetime import datetime
from constants import AGE_CATEGORIES, ADULT_AGE
from loguru import logger


async def handle_birthdate_contact_form(
    self: "ContactFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    birthdate = args.get("birthdate")

    if not utils.appointments.is_valid_date(birthdate):
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Dit juste: 'D<PERSON><PERSON><PERSON>, je n'ai pas compris votre date de naissance. Pouvez-vous répéter?'",
        )
        return

    if isinstance(birthdate, str):
        birthdate = datetime.strptime(birthdate, "%Y-%m-%d")

    self._patient_data.birthdate = birthdate
    self._patient_data.age_category = (
        AGE_CATEGORIES.ADULT.value
        if utils.appointments.is_minimum_age(birthdate, ADULT_AGE)
        else AGE_CATEGORIES.ENFANT.value
    )

    if not self._patient_data.is_new_patient:
        self.possible_patients = self.company_data.possible_patients
        for patient in self.possible_patients:
            if not patient.first_name or not patient.last_name:
                logger.warning(
                    f"Patient {patient} does not have a first name or last name."
                )
                continue

            if self._patient_data.birthdate == patient.birthdate:
                self.possible_patient = patient
                prompt, tools = self.prompts.ask_identity_contact_form(patient)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

    prompt, tools = self.prompts.ask_last_name()
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
