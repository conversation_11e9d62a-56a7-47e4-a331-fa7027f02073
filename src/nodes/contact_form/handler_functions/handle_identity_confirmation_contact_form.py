from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from contact_form.intake_processor import ContactFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)


async def handle_identity_confirmation_contact_form(
    self: "ContactFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    confirm = args["confirm"]
    confirm = confirm == True or "oui" in str(confirm).lower()

    if confirm:
        self._patient_data.set(self.possible_patient)
        self._patient_data.is_new_patient = False
        await self._say_and_wait(
            llm,
            context,
            f"Ravi de vous revoir, {self._patient_data.first_name}",
            no_wait=True,
        )
        return await self.output_function(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
        )

    elif self._patient_data.birthdate and not self._patient_data.last_name:
        prompt, tools = self.prompts.ask_last_name()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    elif (
        self._patient_data.last_name
        and self._patient_data.birthdate
        and not self._patient_data.first_name
    ):
        prompt, tools = self.prompts.ask_first_name()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    return await self.output_function(
        function_name,
        tool_call_id,
        args,
        llm,
        context,
        result_callback,
    )
