from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from ..base.intake_processor import BaseIntakeProcessor
from .prompts import ContactFormPrompts
from typing import Callable, Optional

from .handler_functions.handle_first_name import handle_first_name
from .handler_functions.handle_last_name import handle_last_name
from .handler_functions.handle_new_or_existing_patient import (
    handle_new_or_existing_patient,
)
from .handler_functions.handle_birthdate_contact_form import (
    handle_birthdate_contact_form,
)
from .handler_functions.handle_identity_confirmation_contact_form import (
    handle_identity_confirmation_contact_form,
)

from lib.n8n_client import n8n_client
from constants import Intents
import traceback

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..customer_base.intake_processor import CustomerIntakeProcessor


class ContactFormIntakeProcessor(BaseIntakeProcessor):

    def __init__(
        self,
        base_intake_processor: "CustomerIntakeProcessor",
        output_function: Optional[Callable] = None,
    ):
        self.base_intake_processor = base_intake_processor
        self.phone_caller = base_intake_processor.phone_caller
        self.booking_provider = base_intake_processor.booking_provider
        self._patient_data = base_intake_processor._patient_data
        if output_function:
            self.switcher_output_contact_form_node = output_function

        self.has_custom_output_function = output_function is not None

        self.prompts = ContactFormPrompts(base_intake_processor.company_data)
        self.company_data = base_intake_processor.company_data
        self.possible_patients = self.company_data.possible_patients
        self.possible_patient = None
        self.has_already_asked_if_new_patient = False

    def __init_nodes__(self):
        return super().__init_nodes__()

    async def start_node(self, context, llm, result_callback, return_node=None):
        if not self._patient_data.is_new_patient:
            raise ValueError(
                "User is not a new patient, cannot start contact form intake."
            )

        self._patient_data.reset()
        if not self.has_already_asked_if_new_patient and self.phone_caller.intent in [
            Intents.NOUVEAU,
            Intents.QUESTION,
        ]:
            prompt, tools = self.prompts.ask_new_or_existing_patient(return_node)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        self._patient_data.is_new_patient = False
        prompt, tools = self.prompts.ask_birthdate_contact_form()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_new_or_existing_patient(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_new_or_existing_patient(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_first_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_first_name(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(f"Error in handle_first_name: {e}")
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_last_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_last_name(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(f"Error in handle_last_name: {e}")
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_birthdate_contact_form(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_birthdate_contact_form(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_identity_confirmation_contact_form(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_identity_confirmation_contact_form(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def output_function(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            if not self._patient_data.id:
                self._patient_data.is_new_patient = True

            if (
                self.has_custom_output_function
                and self.switcher_output_contact_form_node
            ):
                await self.switcher_output_contact_form_node(
                    function_name,
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

            if (
                not self.base_intake_processor._patient_data.is_new_patient
                and self.phone_caller.intent
                in [
                    Intents.NOUVEAU,
                    Intents.MODIFIER,
                    Intents.ANNULATION,
                    Intents.CONFIRMER,
                    Intents.RETARD,
                ]
            ):
                await self.base_intake_processor.handle_confirm_identity(
                    "handle_confirm_identity",
                    None,
                    {"confirm": True},
                    llm,
                    context,
                    result_callback,
                )
                return

            if self._patient_data.is_new_patient and self.phone_caller.intent in [
                Intents.NOUVEAU,
                Intents.RETARD,
            ]:
                await self.base_intake_processor.appointment_form_intake_processor.start_node(
                    context,
                    llm,
                    result_callback,
                )
                return

            if self.phone_caller.intent in [
                Intents.MODIFIER,
                Intents.ANNULATION,
                Intents.CONFIRMER,
            ]:
                self.phone_caller.has_note = True
                self.phone_caller.successful_call = True
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message="J'informe le centre médical de votre demande.",
                )
                await self.end_call(
                    "end_call",
                    None,
                    None,
                    llm,
                    context,
                    result_callback,
                    ask_last_question=True,
                )
                return

            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            prompt, tools = self.base_intake_processor.prompts.ask_question(
                self._patient_data
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        except Exception as e:
            n8n_client.send_error_server(
                config=self.base_intake_processor.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )
