from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.base.intake_processor import BaseIntakeProcessor
from nodes.base.tools import BaseTools
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor
from nodes.appointment_form.node_functions import find_motive_by_name
from models import (
    CompanyData,
    PatientData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
from .prompts import CustomerPrompts

from lib.booking_provider import BookingProviderFactory
from constants import (
    Intents,
)

from typing import List, Optional
from locales import get_locale

phrases = get_locale()
import utils.tts
import utils.appointments


class CustomerIntakeProcessor(BaseIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        main_motive_id: int = None,
        minimum_age: Optional[int] = 0,
    ):

        super().__init__(company_data, phone_caller, patient_data)

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = CustomerPrompts(company_data)
        self.company_data.all_in_task = (
            company_data.bot_configuration.all_in_task
            if company_data.bot_configuration.all_in_task is not None
            else True
        )

        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0
        self.is_test = company_data.is_test

        if not main_motive_id:
            main_motive_ids_specialities = list(
                company_data.main_motive_per_speciality.values()
            )
            main_motive_id = (
                main_motive_ids_specialities[0]
                if main_motive_ids_specialities
                else None
            )
            print(
                f"Warning: No main motive ID provided, using first speciality's main motive ID: {main_motive_id}"
            )

        self.main_motive_id = main_motive_id
        self.minimum_age = company_data.bot_configuration.age_minimum or 0
        self.words_boost = company_data.bot_configuration.words_boost_prompt or {}

        print("Words boost configuration:")
        print(self.words_boost)
        print(self.company_data.bot_configuration.instructions_at_end)

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(
            self, self.__switcher_output_contact_form_node
        )

        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self,
            output_function=self.__switcher_output_appointment_form_node,
            main_motive_id=self.main_motive_id,
            min_age=self.minimum_age,
            words_boost=self.words_boost,
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))
        appointment_motive = args.get("appointment_motive")

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback, context, llm, message, [BaseTools.handle_check_intent]
            )
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm=llm,
            context=context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU:
            if self._patient_data.bounced_at:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
                )
                self.phone_caller.successful_call = True
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            is_blocked = utils.appointments.is_patient_blocked_due_to_no_show(
                self._patient_data, self.company_data
            )
            if is_blocked:
                self._patient_data.bounced_at = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
                )
                self.phone_caller.successful_call = True
                self.phone_caller.has_note = True

                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            if appointment_motive:
                find_motive = find_motive_by_name(
                    appointment_motive,
                    self.company_data,
                    None,
                    None,
                )
                if find_motive:
                    self.appointment_form_intake_processor.main_motive_id = (
                        find_motive.visit_motive_id
                    )
                    self.appointment_form_intake_processor.main_motive = find_motive

            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=BaseTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.RETARD:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data,
                    return_node=BaseTools.handle_check_intent,
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.MODIFIER:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=BaseTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.URGENCE:
            await self.forward_call(
                "forward_call",
                None,
                args,
                llm,
                context,
                result_callback,
            )
            return

        elif intent == Intents.ANNULATION:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=BaseTools.handle_check_intent
                )
                context.set_tools(tools)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.QUESTION:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=BaseTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=BaseTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(
                result_callback, context, llm, message, [BaseTools.handle_check_intent]
            )
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data.reset()

        if self.phone_caller.intent == Intents.NOUVEAU:
            if self._patient_data.is_new_patient:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

            await self.appointment_form_intake_processor.start_node(
                context, llm, result_callback
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:

            if self._patient_data.is_identify():
                await self.appointment_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_identify():
                self.phone_caller.successful_call = True
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.contact_form_intake_processor.start_node(
                    context, llm, result_callback
                )
                return

        raise ValueError(f"Intent not {self.phone_caller.intent} not handle")

    async def __switcher_output_contact_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):

        if self.phone_caller.intent == Intents.NOUVEAU:
            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        if self.phone_caller.intent in [
            Intents.MODIFIER,
            Intents.ANNULATION,
            Intents.RETARD,
            Intents.CONFIRMER,
        ]:
            if not self._patient_data.id:
                self.phone_caller.has_note = True

            await self.appointment_form_intake_processor.start_node(
                context,
                llm,
                result_callback,
            )
            return

        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        if self.phone_caller.intent == Intents.QUESTION:
            prompt, tools = self.prompts.ask_question(self._patient_data)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    async def __switcher_output_appointment_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm,
        context,
        result_callback,
    ):

        if (
            self.phone_caller.intent
            in [Intents.NOUVEAU, Intents.MODIFIER, Intents.CONFIRMER]
            and self.appointment_form.is_booking_created
            and self.company_data.bot_configuration.instructions_at_end
        ):
            await self._say_and_wait(
                llm, context, self.company_data.bot_configuration.instructions_at_end
            )

        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )
