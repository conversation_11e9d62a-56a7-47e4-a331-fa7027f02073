from ..base.tools import BaseTools

create_tool_description = BaseTools.create_tool_description


class AppointmentFormTools(BaseTools):

    handle_birthdate_of_patient = create_tool_description(
        name="handle_birthdate_of_patient",
        description="Utilise cette fonction lorsque tu as compris la date de naissance du patient",
        properties=[
            {
                "name": "birthdate",
                "type": "string",
                "description": "La date de naissance du patient en format YYYY-MM-DD. Si le patient donne une mauvaise date, dit lui juste: 'D<PERSON><PERSON><PERSON>, je n'ai pas compris votre date de naissance. Pouvez-vous répéter?'",
                "required": True,
            }
        ],
    )

    handle_motive = create_tool_description(
        name="handle_motive",
        description="Utilise cette fonction lorsque tu as compris le motif du rendez-vous",
        properties=[
            {
                "name": "reason_motive",
                "type": "string",
                "description": "les valeurs peuvent être: le nom du motif, 'non' ou 'oui'",
                "required": True,
            }
        ],
    )

    handle_confirm_motive = create_tool_description(
        name="handle_confirm_motive",
        description="Utilise cette fonction pour confirmer le motif du rendez-vous",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "La confirmation du motif du rendez-vous",
                "required": True,
            }
        ],
    )

    handle_doctor_name = create_tool_description(
        name="handle_doctor_name",
        description="Utilise cette fonction pour comprendre ou confirmer le nom du médecin",
        properties=[
            {
                "name": "doctor_name",
                "type": "string",
                "description": "Le nom du médecin ou 'oui' ou 'non'.",
                "required": True,
            }
        ],
    )

    handle_appointment_datetime = create_tool_description(
        name="handle_appointment_datetime",
        description="Après chaque réponse de l'utilisateur, utilise cette fonction pour répondre à toute demande liée à la date ou l'heure du rendez-vous.",
        properties=[
            {
                "name": "appointment_datetime",
                "type": "string",
                "description": (
                    f"""
                      
                 Attendre la réponse du patient et agir en conséquence :**
                 Appeler OBLIGATOIREMENT la fonction `handle_appointment_datetime` après chaque réponse du patient**

              Tu dois TOUJOURS appeler cette fonction, même si tu n'es pas certain de la réponse. Ne réponds JAMAIS en langage naturel à ce stade. Voici les cas :

              ✅ Réponses du patient → Appel de fonction :

              - "aucun", "encore", "non", "autre"  
                → `handle_appointment_datetime("non")`

              - "oui", "confirme"  
                → `handle_appointment_datetime(appointment_datetime="YYYY-MM-DD HH:MM")` ← utilise la **dernière date et heure proposées**

              - Propose une autre **heure**  
                → garde la **date proposée** et change seulement l'heure

              - Propose une autre **date**  
                → garde **00:00** comme heure

              - Donne un **jour de la semaine**  
                → `appointment_day="lundi"` (par exemple), `appointment_datetime=None`

              - Donne un **mois**  
                → interprète comme le **1er du mois à 00:00**

              - Demande "matin"  
                → `appointment_datetime="YYYY-MM-DD 06:59", with_half_matin_aprem=True`

              - Demande "après-midi"  
                → `appointment_datetime="YYYY-MM-DD 13:59", with_half_matin_aprem=True`

              ---

              🛑 **INTERDIT** :
              - ❌ Ne confirme JAMAIS le rendez-vous.
              - ❌ Ne réponds PAS au patient par du texte ici.
              - ❌ Ne mentionne pas le secrétariat, sauf pour dire que la prise se fait uniquement via l'’'assistant.
              - ❌ N'utilise JAMAIS la fonction `handle_appointment_day`.

              👉 Tu dois **terminer ta réponse uniquement avec un appel à `handle_appointment_datetime`**.
                    
                    """
                ),
                "required": True,
            },
            {
                "name": "appointment_day",
                "type": "string",
                "description": (
                    "Jour de la semaine spécifié par le patient (ex: 'lundi, mardi, mercredi, jeudi, vendredi, samedi, dimanche'). "
                    "À remplir si le patient donne un jour explicite sans date."
                ),
                "required": False,
                "enum": [
                    "lundi",
                    "mardi",
                    "mercredi",
                    "jeudi",
                    "vendredi",
                    "samedi",
                    "dimanche",
                    None,
                ],
            },
            {
                "name": "with_half_matin_aprem",
                "type": "boolean",
                "description": (
                    "Mettre à True si le patient demande spécifiquement 'le matin' ou 'l’après-midi'. "
                    "Sinon, mettre False ou laisser vide."
                ),
                "required": False,
                "enum": [True, False],
            },
        ],
    )

    handle_appointment_day = create_tool_description(
        name="handle_appointment_day",
        description="Utilise cette fonction pour confirmer la date de rendez-vous",
        properties=[
            {
                "name": "day",
                "type": "string",
                "description": "Le jour de rendez-vous",
                "required": False,
                "enum": [
                    "lundi",
                    "mardi",
                    "mercredi",
                    "jeudi",
                    "vendredi",
                    "samedi",
                    "dimanche",
                    None,
                ],
            },
            {
                "name": "date",
                "type": "string",
                "description": "La date de rendez-vous en format YYYY-MM-DD si la date a été precisée",
                "required": False,
            },
        ],
    )

    suggest_a_day = create_tool_description(
        name="suggest_a_day",
        description="Utilise cette fonction pour proposer une autre jour pour le rendez-vous",
    )

    handle_appointment_time = create_tool_description(
        name="handle_appointment_time",
        description="Utilise cette fonction pour confirmer l'heure de rendez-vous",
        properties=[
            {
                "name": "time",
                "type": "string",
                "description": "L'heure de rendez-vous en format HH:MM avec l'heure souhaitée",
                "required": True,
            },
            {
                "name": "date",
                "type": "string",
                "description": "La date de rendez-vous en format YYYY-MM-DD",
                "required": True,
            },
        ],
    )

    handle_appointment_confirmation = create_tool_description(
        name="handle_appointment_confirmation",
        description="Utilise cette fonction pour confirmer le rendez-vous",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "Si le patient dit 'oui' alors la valeur est True sinon False",
                "required": True,
                "enum": [True, False],
            }
        ],
    )

    handle_confirm_appointment_metadata = create_tool_description(
        name="handle_confirm_appointment_metadata",
        description="Utilise cette fonction pour confirmer le rendez-vous",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "La confirmation du rendez-vous",
                "required": True,
                "enum": [True, False],
            }
        ],
    )

    handle_cancel_appointment = create_tool_description(
        name="handle_cancel_appointment",
        description="Utilise cette fonction pour annuler le rendez-vous",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "La confirmation de l'annulation du rendez-vous",
                "required": True,
                "enum": [True, False],
            }
        ],
    )

    handle_more_information_for_motive = create_tool_description(
        name="handle_more_information_for_motive",
        description="Utilise cette fonction pour décrire la raison du rendez-vous",
        properties=[
            {
                "name": "reason",
                "type": "string",
                "description": "La raison du rendez-vous",
                "required": True,
            }
        ],
    )

    handle_with_echo_mammaire = create_tool_description(
        name="handle_with_echo_mammaire",
        description="Utilise cette fonction pour confirmer si l'autre motif est néecessaire",
        properties=[
            {
                "name": "confirm",
                "type": "string",
                "description": "La confirmation si l'autre motif est nécessaire",
                "required": True,
                "enum": [
                    "oui",
                    "non",
                ],
            }
        ],
    )

    handle_check_if_injection = create_tool_description(
        name="handle_check_if_injection",
        description="Utilise cette fonction pour vérifier si une injection est prescrite avec le scanner ou l'IRM",
        properties=[
            {
                "name": "has_injection",
                "type": "boolean",
                "description": "Confirmation de la présence ou non d'une injection avec le scanner ou l'IRM",
                "required": True
            }
        ],
    )

    handle_check_for_injection_allergies = create_tool_description(
        name="handle_check_for_injection_allergies",
        description="Utilise cette fonction pour vérifier si le patient a une allergie au produit de contraste iodé",
        properties=[
            {
                "name": "confirm",
                "type": "string",
                "description": "Confirmation de la présence ou non d'allergie au produit de contraste iodé",
                "required": True,
                "enum": [
                    "oui",
                    "non",
                ],
            }
        ],
    )

    handle_check_for_mri_counter_indications = create_tool_description(
        name="handle_check_for_mri_counter_indications",
        description="Utilise cette fonction pour vérifier les contre-indications pour un examen IRM",
        properties=[
            {
                "name": "confirm",
                "type": "string",
                "description": "Confirmation de la présence ou non de contre-indications à l'IRM",
                "required": True,
                "enum": [
                    "oui",
                    "non",
                ],
            }
        ],
    )

    handle_motive_condition = create_tool_description(
        name="handle_motive_condition",
        description="Utilise cette fonction pour confirmer si le patient remplit la condition",
        properties=[
            {
                "name": "confirm",
                "type": "string",
                "description": "La confirmation si le patient remplit la condition",
                "required": True,
                "enum": [
                    "oui",
                    "non",
                ],
            }
        ],
    )

    handle_reason_of_cancellation = create_tool_description(
        name="handle_reason_of_cancellation",
        description="Utilise cette fonction pour comprendre la raison de l'annulation",
        properties=[
            {
                "name": "reason",
                "type": "string",
                "description": "La raison de l'annulation, ca doit être la raison complète de l'annulation",
                "required": True,
            }
        ],
    )

    handle_continue_with_same_doctor = create_tool_description(
        name="handle_continue_with_same_doctor",
        description="Utilise cette fonction pour confirmer si le patient veut continuer avec le même médecin",
        properties=[
            {
                "name": "confirm",
                "type": "boolean",
                "description": "La confirmation si le patient veut continuer avec le même médecin",
                "required": True,
                "enum": [True, False],
            }
        ],
    )

    handle_how_long_late = create_tool_description(
        name="handle_how_long_late",
        description="Utilise cette fonction pour comprendre combien de temps le patient sera en retard",
        properties=[
            {
                "name": "minutes_late",
                "type": "number",
                "description": "Le temps en minutes que le patient sera en retard",
                "required": True,
            }
        ],
    )

    handle_speciality = BaseTools.create_tool_description(
        name="handle_speciality",
        description="Handle service type",
        properties=[
            {
                "name": "speciality",
                "type": "string",
                "description": "Utilise cette fonction pour donner la spécialité du service",
                "required": True,
                "enum": lambda self: self.speciality_names,
            }
        ],
    )

    handle_how_many_motives = BaseTools.create_tool_description(
        name="handle_how_many_motives",
        description="Handle how many motives",
        properties=[
            {
                "name": "number_of_motives",
                "type": "number",
                "description": "Utilise cette fonction pour donner le nombre de motifs / examen souhaité. ",
                "required": True,
                "enum": [
                    1,
                    2,
                ],
            }
        ],
    )
