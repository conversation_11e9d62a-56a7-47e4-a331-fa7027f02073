from typing import Optional, List, Callable, Tuple
from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from constants import (
    Intents,
    MOTIFS_WITH_CONDITIONS,
    ClientType,
)
import utils.tts
from .handler_functions.handle_check_for_mri_counter_indications import (
    handle_check_for_mri_counter_indications,
)
from ..base.intake_processor import BaseIntakeProcessor
from .prompts import AppointmentFormPrompts
from .node_functions import find_motive_by_id
from loguru import logger

import utils.appointments
from locales import get_locale
from models import (
    MotiveAppointment,
    FunctionCall,
    PatientData,
    PhoneCaller,
    BookingProviderType,
    Appointment,
)
from lib.doctolib_client import DoctolibClient
from lib.edl_client import EdlClient
from lib.n8n_client import n8n_client
from lib.n8n_client import N8NClient
import traceback
from models import AppointmentForm

from .handler_functions import (
    handle_birthdate_of_patient,
    handle_doctor_name,
    handle_motive,
    handle_confirm_motive,
    handle_appointment_datetime,
    handle_appointment_day,
    handle_appointment_confirmation,
    handle_confirm_appointment_metadata,
    handle_more_information_for_motive,
    handle_with_echo_mammaire,
    handle_motive_condition,
    handle_reason_of_cancellation,
    handle_continue_with_same_doctor,
    handle_how_long_late,
    handle_speciality,
    handle_how_many_motives,
    handle_check_for_injection_allergies,
    handle_check_if_injection,
)

phrases = get_locale()


class AppointmentFormIntakeProcessor(BaseIntakeProcessor):

    def __init__(
        self,
        self_child: BaseIntakeProcessor,
        output_function: FunctionCall,
        main_motive_id: Optional[int] = None,
        min_age: Optional[int] = 0,
        max_age: Optional[int] = 140,
        motif_alias: Optional[dict[int]] = None,
        motives_ids_with_reasons: Optional[List[int]] = [],
        should_transfer_motive: Optional[
            Callable[[str, MotiveAppointment, PatientData, PhoneCaller], bool]
        ] = None,
        words_boost: Optional[List[Tuple[str, str]]] = [],
    ):
        self._child = self_child
        company_data = self_child.company_data
        phone_caller = self_child.phone_caller
        patient_data = self_child._patient_data
        appointment_form = self_child.appointment_form
        super().__init__(company_data, phone_caller, patient_data)

        if company_data.booking_provider == BookingProviderType.EDL:
            self.booking_provider = EdlClient(company_data)
        elif company_data.booking_provider == BookingProviderType.DOCTOLIB:
            self.booking_provider = DoctolibClient(company_data)
        else:
            raise ValueError(
                f"Booking provider {company_data.booking_provider} not supported"
            )

        self.n8n_client = N8NClient()
        self.phrases = phrases
        self.output_function = output_function
        self.ask_speciality = self.company_data.bot_configuration.ask_speciality
        self.specialities = self.company_data.inbound_config_file.specialities

        self.phone_caller = phone_caller
        self.company_data = company_data
        self._patient_data = patient_data
        self.appointment_form = appointment_form
        self.next_availabilities = self_child.next_availabilities
        self.agenda_ids_to_check = self_child.company_data.agenda_id
        self.motives_ids_with_reasons = motives_ids_with_reasons
        self.motives_with_condition = MOTIFS_WITH_CONDITIONS.get(
            self.company_data.config, {}
        )
        self.should_transfer_motive = should_transfer_motive
        self.words_boost = words_boost
        print(f"Boosted {self.words_boost}")

        self.current_to_premieres_consultations = (
            self_child.company_data.current_to_premiere_motive
        )
        self.adult_to_enfants_consultations = (
            self_child.company_data.adult_to_child_motive
        )

        self.main_motive_per_speciality = self.company_data.main_motive_per_speciality

        self.main_motive_id = main_motive_id
        self.main_motive = None

        self.say_name_doctor = self.company_data.bot_configuration.say_name_doctor
        self.ask_doctor_name = self.company_data.bot_configuration.ask_name_doctor
        self.minimum_age = min_age
        self.maximum_age = max_age
        self.motif_alias = motif_alias

        self.retries_take_appointment = 0
        self.old_appointment_form = None
        self.black_list_appointments: List[Appointment] = []
        self.suggested_appointments: List[Appointment] = []
        self.has_asked_to_change_doctor = False

        self.has_confirm_doctor = False
        self.has_confirm_motive = False
        self.has_confirm_datetime = False
        self.has_confirm_appointment = False

        self.prompts = AppointmentFormPrompts(self.company_data)

    def __init_nodes__(self):
        pass

    async def start_node(self, context, llm, result_callback, return_node=None):
        if not self.appointment_form:
            logger.warning(
                f"Appointment form is not initialized for {self.phone_caller.phone_number}"
            )
            self.appointment_form = AppointmentForm(
                config=self.company_data.config,
            )
            self._child.appointment_form = self.appointment_form

        if self.phone_caller.intent == Intents.NOUVEAU:

            if (
                self.company_data.client_type == ClientType.IMAGING
                and not self.company_data.bot_configuration.ask_speciality
                and not self.company_data.config_name in ["icpc"]
            ):
                prompt, tools = self.prompts.ask_how_many_motives()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            if self.ask_speciality:
                prompt, tools = self.prompts.ask_speciality(
                    specialities=self.specialities,
                    patient_data=self._patient_data,
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            if (
                not self.ask_speciality
                and not self.main_motive_id
                and self.main_motive_per_speciality
            ):
                main_motives = self.main_motive_per_speciality.values()
                if len(main_motives) == 1:
                    self.main_motive_id = list(main_motives)[0]

            self.main_motive = find_motive_by_id(
                self.main_motive_id,
                self.company_data,
            )

            print(self.main_motive)

            if not self._patient_data.birthdate:
                prompt, tools = self.prompts.ask_birthdate_of_patient(
                    return_node=return_node
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                await self.handle_birthdate_of_patient(
                    "handle_birthdate_of_patient",
                    None,
                    {"birthdate": self._patient_data.birthdate},
                    llm,
                    context,
                    result_callback,
                )
                return

        if self.company_data.config in [
            "config86",
            "config83",
        ] and self.phone_caller.intent in [Intents.MODIFIER, Intents.ANNULATION]:
            motive = find_motive_by_id(
                self.appointment_form.visit_motive_id,
                self.company_data,
            )
            if not motive:
                await self.forward_call(
                    "forward_call", None, {}, llm, context, result_callback
                )
                return
            if motive.speciality_id == 11:
                await self.forward_call(
                    "forward_call", None, {}, llm, context, result_callback
                )
                return

        if (
            len(self.appointment_form.steps) > 1
            and not self.company_data.client_type == ClientType.DEFAULT
        ):
            await self.forward_call(
                "forward_call", None, {}, llm, context, result_callback
            )
            return

        if self.phone_caller.intent == Intents.MODIFIER:
            self.old_appointment_form = self._patient_data.next_appointment
            if utils.appointments.is_appointment_has_passed(self.old_appointment_form):
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message="J'informe le centre médical de votre demande.",
                )
                self.phone_caller.has_note = True

                await self.end_call(
                    "end_call",
                    None,
                    None,
                    llm,
                    context,
                    result_callback,
                    ask_last_question=True,
                )
                return

            await self._say_and_wait(
                llm=llm,
                context=context,
                message="Un instant s'il-vous-plaît, je cherche votre rendez-vous.",
            )

            motive = find_motive_by_id(
                self.old_appointment_form.visit_motive_id,
                self.company_data,
                self.old_appointment_form.medecin,
            )

            if not motive:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    """
                      Je suis désolé, je n'ai pas trouvé votre rendez-vous.
                      Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps.
                    """,
                )

                await self.end_call(
                    "end_call",
                    None,
                    None,
                    llm,
                    context,
                    result_callback,
                    ask_last_question=True,
                )
                return

            self.appointment_form.visit_motive_id = motive.visit_motive_id
            self.appointment_form.visit_motive_name = motive.visit_motive_name
            self.appointment_form.instructions = (
                motive.instructions if motive.instructions else ""
            )
            self.appointment_form.is_open_motive = motive.open
            self.appointment_form.type = motive.type.lower() if motive.type else ""
            self.appointment_form.speciality_id = motive.speciality_id
            self.appointment_form.medecin = self.old_appointment_form.medecin

            mod_date = (
                utils.appointments.format_datetime_with_day_in_french(
                    self.old_appointment_form.start_date
                )
                if self.company_data.has_unique_agenda
                else utils.appointments.format_appointment_to_datetime_with_day_in_french(
                    self.old_appointment_form
                )
            ).split("(")[0]

            message = f"J'ai trouvé votre rendez-vous du {mod_date}."
            if len(self.old_appointment_form.steps) > 1:
                medecins = [
                    step.get("medecin")
                    for step in self.old_appointment_form.steps
                    if step.get("medecin") and len(step.get("medecin").split(" ")) > 1
                ]
                medecins = list(dict.fromkeys(medecins))
                if len(medecins) > 1:
                    mod_date = utils.appointments.format_datetime_with_day_in_french(
                        self.old_appointment_form.start_date
                    ).split("(")[0]
                    message = f"J'ai trouvé vos rendez-vous du {mod_date} avec le docteur {' et docteur '.join(medecins)}."
                else:
                    message = f"J'ai trouvé vos rendez-vous du {mod_date}."

                self.appointment_form.number_of_motives = len(
                    self.old_appointment_form.steps
                )
                steps_motives: List[MotiveAppointment] = []
                for step in self.old_appointment_form.steps:
                    if not step:
                        continue
                    motive = find_motive_by_id(
                        step.get("visit_motive_id"),
                        self.company_data,
                    )
                    if motive:
                        steps_motives.append(motive)

                self.appointment_form.steps_motives = steps_motives

            await self._say_and_wait(
                llm=llm,
                context=context,
                message=message,
                no_wait=True,
            )

            prompt, tools = self.prompts.ask_confirm_reason_motive(
                self.appointment_form.visit_motive_name, intent=Intents.MODIFIER
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.ANNULATION:
            self.appointment_form = self._patient_data.next_appointment

            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message="""Je n'ai pas trouvé votre rendez-vous.
                          Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps.
                        """,
                    no_wait=True,
                )
                await self.end_call(
                    "end_call",
                    None,
                    None,
                    llm,
                    context,
                    result_callback,
                    ask_last_question=True,
                )
                return

            if not self.appointment_form:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message="""Je n'ai pas trouvé votre rendez-vous.
                                    Je notifie le centre médical, vous serez recontacté sous peu de temps.
                                """,
                    no_wait=True,
                )
                await self.end_call(
                    "end_call", None, None, llm, context, result_callback
                )
                return

            prompt, tools = self.prompts.ask_reason_of_cancellation(
                self.phone_caller.intent, steps=self.appointment_form.steps
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.RETARD:
            prompt, tools = self.prompts.ask_how_long_late(
                self._patient_data.is_identify()
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.CONFIRMER:
            if self._patient_data.is_new_patient:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je notifie le centre médical de votre confirmation de rendez-vous.",
                )
                await self.end_call(
                    "end_call", None, None, llm, context, result_callback
                )
                return

            if utils.appointments.is_appointment_has_passed(
                self._patient_data.next_appointment
            ):
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message="""Je n'ai pas trouvé votre rendez-vous.
                          Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps.
                        """,
                    no_wait=True,
                )
                await self.end_call(
                    "end_call", None, None, llm, context, result_callback
                )
                return

            self.appointment_form = self._patient_data.next_appointment
            prompt, tools = self.prompts.ask_appointment_confirmation(
                self.appointment_form, self.phone_caller.intent
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    async def handle_birthdate_of_patient(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_birthdate_of_patient(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_speciality(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_speciality(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_doctor_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_doctor_name(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_how_many_motives(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_how_many_motives(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_motive(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_motive(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_confirm_motive(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_confirm_motive(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_appointment_datetime(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_appointment_datetime(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_appointment_day(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_appointment_day(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_appointment_confirmation(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_appointment_confirmation(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_confirm_appointment_metadata(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_confirm_appointment_metadata(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_more_information_for_motive(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_more_information_for_motive(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_with_echo_mammaire(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_with_echo_mammaire(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_check_if_injection(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_check_if_injection(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_check_for_injection_allergies(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_check_for_injection_allergies(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_check_for_mri_counter_indications(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_check_for_mri_counter_indications(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_motive_condition(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_motive_condition(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_reason_of_cancellation(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_reason_of_cancellation(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_continue_with_same_doctor(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_continue_with_same_doctor(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )

    async def handle_how_long_late(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        try:
            await handle_how_long_late(
                self,
                function_name,
                tool_call_id,
                args,
                llm,
                context,
                result_callback,
            )
        except Exception as e:
            print(e)
            n8n_client.send_error_server(
                config=self.company_data.config,
                phone_caller=self.phone_caller,
                traceback=traceback.format_exc(),
            )
