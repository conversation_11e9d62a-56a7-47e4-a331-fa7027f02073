from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from ..node_functions import find_motive_by_id


async def handle_motive_condition(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    confirm = args.get("confirm")
    confirm = confirm == True or "oui" in str(confirm).lower()

    if confirm:
        if self.appointment_form.visit_motive_id == 152685:
            self.phone_caller.successful_call = True
            await self._say_and_wait(
                llm,
                context,
                "Nous ne proposons malheureusement plus de séances de kinésithérapie suite à un accident du travail. Nous vous invitons à contacter un centre habilité à les prendre en charge.",
                no_wait=True,
            )
            await self.end_call(
                "end_call",
                None,
                args,
                llm,
                context,
                result_callback,
                ask_last_question=True,
            )
            return

    if not confirm:
        if self.appointment_form.visit_motive_id == 259880:
            await self._say_and_wait(
                llm,
                context,
                "Avant de pouvoir prendre rendez-vous pour une consultation de rythmologie, vous devez d'abord consulter un cardiologue.",
            )
            self.main_motive = find_motive_by_id(
                304195,
                self.company_data,
                self.appointment_form.medecin,
            )

            prompt, tools = self.prompts.ask_motive(self.main_motive)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        ### Pour Olonne
        if self.company_data.config in ["config84"]:
            await self.forward_call(
                "forward_call", None, args, llm, context, result_callback
            )
            return

    await self.handle_confirm_motive(
        function_name,
        tool_call_id,
        {
            "confirm": True,
            "has_done_condition": True,
        },
        llm,
        context,
        result_callback,
    )
