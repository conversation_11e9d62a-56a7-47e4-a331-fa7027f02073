from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from ..node_functions import find_motive_by_id
from datetime import datetime, timedelta
from constants import Intents
from models import Appointment, Doctor, AppointmentForm
from pytz import timezone
from utils import remove_accents


async def handle_how_long_late(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    def get_full_name():
        if (
            self._patient_data
            and self._patient_data.first_name
            and self._patient_data.last_name
        ):
            first_name = self._patient_data.first_name.capitalize()
            last_name = self._patient_data.last_name.capitalize()
            return f"{first_name} {last_name}"
        else:
            return "Le patient"

    def get_appointment_description(appointment: Appoint<PERSON><PERSON><PERSON>, doctor: Doctor):
        time_str = appointment.start_date.strftime("%H:%M")
        doctor_name = (
            f"avec Docteur {doctor.name.split('(')[0]}"
            if doctor and doctor.practitioner_id
            else ""
        )
        return f"rendez-vous pour {appointment.visit_motive_name} à {time_str} {doctor_name}"

    def is_same_day_appointment(appointment: Appointment):
        return appointment.start_date.date() == appointment.end_date.date()

    async def forward():
        await self.forward_call(
            function_name, tool_call_id, args, llm, context, result_callback
        )

        # Ensure start_date is timezone-aware in Paris time

    def ensure_paris_timezone(dt: datetime) -> datetime:
        if dt.tzinfo is None or dt.tzinfo.utcoffset(dt) is None:
            return paris_tz.localize(dt)
        return dt.astimezone(paris_tz)

    # Validate input
    try:
        how_long_late_min = int(args.get("minutes_late"))
    except (ValueError, TypeError):
        return await forward()

    next_appointment = (
        self._patient_data.next_appointment if self._patient_data else None
    )
    full_name = get_full_name()

    self.phone_caller.has_note = True

    if next_appointment:
        try:


            visit_motive_name = next_appointment.visit_motive_name or ""

            if self.company_data.config in ["config91"] and visit_motive_name:
                if (
                    "depose" in remove_accents(visit_motive_name).lower()
                    or "pose" in remove_accents(visit_motive_name).lower()
                    or "endodontie" in remove_accents(visit_motive_name).lower()
                    or (
                        "endo" in remove_accents(visit_motive_name).lower()
                        and "taille" in remove_accents(visit_motive_name).lower()
                    )
                    or "blanchiment" in remove_accents(visit_motive_name).lower()
                    or "extraction" in remove_accents(visit_motive_name).lower()
                    or "taille" in remove_accents(visit_motive_name).lower()
                    and "empreinte" in remove_accents(visit_motive_name).lower()
                ):
                    await self.forward_call(
                        "forward_call", None, {}, llm, context, result_callback
                    )
                    return

            # Add note if same-day and appointment has an ID
            if next_appointment.id and is_same_day_appointment(next_appointment):
                await self.booking_provider.update_appointment_with_note(
                    next_appointment,
                    f"Vocca: {full_name} sera en retard de {how_long_late_min} minutes.",
                )
                if self.company_data.config in ["config91", "config135"]:
                    self.phone_caller.has_note = False

            # Handle late arrival limit
            motive_id = next_appointment.visit_motive_id
            if motive_id:
                motive = find_motive_by_id(motive_id, self.company_data)

                # Hardcoded Clichy REA Laser case
                if (
                    motive.visit_motive_id == 603433
                    or next_appointment.agenda_id == 153483
                ) and how_long_late_min >= 5:
                    return await self.forward_call(
                        "forward_call", None, {}, llm, context, result_callback
                    )

                # Handle speciality late limit
                if motive and motive.speciality_id:
                    speciality = next(
                        (
                            s
                            for s in self.company_data.inbound_config_file.specialities
                            if s.id == motive.speciality_id
                        ),
                        None,
                    )

                    if (
                        self.company_data.config == "config91"
                        and "gyné" in motive.visit_motive_name.lower()
                    ):
                        speciality = next(
                            (
                                s
                                for s in self.company_data.inbound_config_file.specialities
                                if "gyné" in s.name.lower()
                            ),
                            speciality,
                        )

                    if speciality and speciality.late_arrival_limit:
                        paris_tz = timezone("Europe/Paris")
                        now_in_paris = datetime.now(paris_tz)

                        appointment_start = ensure_paris_timezone(
                            next_appointment.start_date
                        )

                        if appointment_start.date() != now_in_paris.date():
                            await self.forward_call(
                                "forward_call", None, {}, llm, context, result_callback
                            )
                            return

                        late_limit_time = str(speciality.late_arrival_limit)
                        if how_long_late_min >= speciality.late_arrival_limit:
                            msg = f"En raison d'un retard supérieur à {late_limit_time} minutes, le centre est malheureusement contraint d'annuler votre examen. Nous vous invitons à reprendre rendez-vous à un autre moment. Merci de votre compréhension."
                            await self.booking_provider.cancel_appointment(
                                next_appointment, Intents.ANNULATION, "no_show"
                            )
                            await self._say_and_wait(
                                llm,
                                context,
                                msg,
                            )
                            how_long_late_min_str = str(how_long_late_min)
                            next_appointment.notes = f"Vocca: RDV annulé car le retard est de {how_long_late_min_str} minutes."

                            await self.booking_provider.cancel_appointment(
                                next_appointment, Intents.ANNULATION, "no_show"
                            )
                            await self.end_call(
                                "end_call", None, {}, llm, context, result_callback
                            )
                            return

                        else:
                            msg = f"Si votre retard est supérieur à {late_limit_time} minutes, le centre devra malheureusement être contraint d'annuler votre examen."
                            await self._say_and_wait(llm, context, msg)

        except Exception as e:
            print(f"Non-blocking appointment info error: {e}")

    # Default: notify of lateness and end call
    await self._say_and_wait(llm, context, "Je notifie votre retard au secrétariat")

    task_msg = f"Je ne dis rien maintenant mais quand je créerai la tâche, j'écris:  Vocca: {full_name} sera en retard de {how_long_late_min} minutes"
    context.add_message({"role": "assistant", "content": task_msg})

    await self.end_call(
        "end_call",
        None,
        args,
        llm,
        context,
        result_callback,
        True,
    )
