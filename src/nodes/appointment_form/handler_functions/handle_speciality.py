from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from rapidfuzz import fuzz
import utils
from nodes.appointment_form.tools import AppointmentFormTools
from nodes.appointment_form.node_functions import find_motive_by_id
from constants import Intents


async def handle_speciality(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    specialities_names = [
        speciality.name.lower()
        for speciality in self.company_data.inbound_config_file.specialities
    ]
    arg_speciality = str(args.get("speciality")) if args.get("speciality") else None
    if "général" in arg_speciality.lower():
        arg_speciality = "généraliste"
    if "dentaire" in arg_speciality.lower() or "dentiste" in arg_speciality.lower():
        arg_speciality = "dentiste"
    if "kine" in utils.remove_accents(arg_speciality).lower():
        arg_speciality = "Masseur-kinésithérapeute"
    if "dermato" in arg_speciality.lower():
        arg_speciality = "Dermatologue et vénérologue"

    # Find best matching speciality using fuzzy matching
    best_match = None
    best_ratio = 0
    for item in specialities_names:
        items = item.split()

        ratio = fuzz.ratio(
            utils.remove_accents(arg_speciality).lower(),
            utils.remove_accents(item).lower(),
        )

        if ratio == 100:
            best_match = item
            best_ratio = ratio
            break

        if len(items) > 1:
            for sub_item in items:
                ratio = max(
                    ratio,
                    fuzz.ratio(
                        utils.remove_accents(arg_speciality).lower(),
                        utils.remove_accents(sub_item).lower(),
                    ),
                )

        if ratio > best_ratio:
            best_ratio = ratio
            best_match = item

    if best_ratio < 90:  # Threshold for acceptable match
        self.appointment_form.type = None
        self.appointment_form.speciality_id = None
        msg = f"Nous n'avons pas la spécialité {arg_speciality}."
        await self._reply_to_user(
            result_callback, context, llm, msg, [AppointmentFormTools.handle_speciality]
        )
        return

    speciality = best_match

    speciality_found = next(
        (
            item
            for item in self.company_data.inbound_config_file.specialities
            if speciality.lower() == item.name.lower()
        ),
        None,
    )

    self.appointment_form.type = speciality_found.name.lower()
    self.appointment_form.speciality_id = speciality_found.id

    speciality_id_for_main_motive = speciality_found.id
    if speciality_found.alias_id:
        speciality_found.id = speciality_found.alias_id

    if not self.main_motive_id and speciality_found.id != self.main_motive_id:
        self.main_motive_id = self.company_data.main_motive_per_speciality.get(
            speciality_id_for_main_motive, None
        )
        if self.main_motive_id:
            self.main_motive = find_motive_by_id(
                self.main_motive_id,
                self.company_data,
            )

    if (
        "radio" in self.appointment_form.type.lower()
        or "echo" in self.appointment_form.type.lower()
    ):
        prompt, tools = self.prompts.ask_how_many_motives()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if speciality.lower() == "orl" and self.company_data.config == "config79":
        await self.forward_call(
            "forward_call",
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
        )
        return

    if (
        self.company_data.config == "config88"
        and "psychiatre" in speciality.lower()
        and self._patient_data.is_new_patient
        and self.phone_caller.intent == Intents.NOUVEAU
    ):
        await self._say_and_wait(
            llm,
            context,
            "Je suis désolé, mais nous ne pouvons pas prendre en charge de nouveaux patients pour le moment.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback, True)

    if (
        self.company_data.config == "config108"
        and self._patient_data.is_identify()
        and "denti" in speciality.lower()
    ):
        historical_doctor_dentist = self._patient_data.historical_doctors.get(1, None)
        if historical_doctor_dentist and historical_doctor_dentist.agenda_id == 1234622:
            await self.forward_call(
                "forward_call", tool_call_id, args, llm, context, result_callback
            )
            return

    if self.company_data.config == "config87" and speciality_found.id == 6:
        medecin_historique = self._patient_data.historical_doctors.get(
            speciality_found.id, None
        )
        if self._patient_data.is_new_patient or (
            medecin_historique is None or medecin_historique.agenda_id != 405464
        ):
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, mais nous ne pouvons pas prendre en charge de nouveaux patients pour le moment.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

    if not self._patient_data.birthdate:
        prompt, tools = self.prompts.ask_birthdate_of_patient()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return
    else:
        await self.handle_birthdate_of_patient(
            "handle_birthdate_of_patient",
            None,
            {"birthdate": self._patient_data.birthdate},
            llm,
            context,
            result_callback,
        )
        return
