from typing import TYPE_CHECKING
from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from ..node_functions import (
    get_main_motive,
    find_motive_by_id,
)
import utils.appointments
from loguru import logger
from ..tools import AppointmentFormTools

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor


async def handle_doctor_name(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    arg_doctor_name = args["doctor_name"]

    if not arg_doctor_name:
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Dés<PERSON><PERSON>, je n'ai pas compris votre raison. Pouvez-vous répéter?",
            [AppointmentFormTools.handle_doctor_name],
        )
        return

    if arg_doctor_name == "autre":
        self.main_motive = await get_main_motive(
            self._patient_data,
            self.main_motive,
            self.current_to_premieres_consultations,
            self.adult_to_enfants_consultations,
            self.company_data,
        )
        prompt, tools = self.prompts.ask_motive(
            self.main_motive,
            words_boost=self.words_boost,
        )
        self.appointment_form.medecin = None
        self.appointment_form.practitioner_id = None
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    doctors = await self.booking_provider.get_doctors()
    if arg_doctor_name == "non":
        self.appointment_form.medecin = None
        prompt, tools = self.prompts.ask_doctor_name(doctors)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if "oui" in str(arg_doctor_name).lower() and self.appointment_form.medecin:
        arg_doctor_name = self.appointment_form.medecin
        doctor = utils.appointments.get_doctor_by_name(arg_doctor_name, doctors)
        if doctor and doctor.speciality_id != self.appointment_form.speciality_id:
            self.appointment_form.speciality_id = doctor.speciality_id
            logger.info(
                f"Doctor {doctor.name} has a different speciality ({doctor.speciality_id}) than the current appointment form speciality ({self.appointment_form.speciality_id}). Updating speciality."
            )

    doctor = utils.appointments.get_doctor_by_name(arg_doctor_name, doctors)
    if not doctor:
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Désolé, je n'ai pas compris le nom du docteur. Pouvez-vous répéter?",
            [AppointmentFormTools.handle_doctor_name],
        )
        return

    if not utils.appointments.is_minimum_age(
        self._patient_data.birthdate, doctor.age_minimum
    ):
        age_str = str(int(doctor.age_minimum))
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, le patient doit avoir au moins {age_str} ans pour prendre rendez-vous avec le docteur {doctor.name.lower()}.",
        )
        suggest_doctors = await self.booking_provider.get_doctors(self._patient_data)
        if not suggest_doctors:
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, je n'ai pas trouvé de docteur disponible.",
            )
            await self.end_call(
                "end_call",
                None,
                args,
                llm,
                context,
                result_callback,
                ask_last_question=True,
            )
            return
        suggest_doctor = suggest_doctors[0]
        self.appointment_form.medecin = suggest_doctor.name
        prompt, tools = self.prompts.ask_doctor_name(doctors, suggest_doctor)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if not utils.appointments.is_maximum_age(
        self._patient_data.birthdate, doctor.age_maximum
    ):
        age_str = str(int(doctor.age_maximum))
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, seul les patients de moins de {age_str} ans peuvent prendre rendez-vous avec le docteur {doctor.name.lower()}.",
        )

        suggest_doctors = await self.booking_provider.get_doctors(self._patient_data)
        if not suggest_doctors:
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, je n'ai pas trouvé de docteur disponible.",
            )
            await self.end_call(
                "end_call",
                None,
                args,
                llm,
                context,
                result_callback,
                ask_last_question=True,
            )
            return

        suggest_doctor = suggest_doctors[0]
        self.appointment_form.medecin = suggest_doctor.name
        prompt, tools = self.prompts.ask_doctor_name(doctors, suggest_doctor)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    print(doctor)
    if (
        self.appointment_form.speciality_id
        and self.appointment_form.speciality_id is not None
        and doctor.speciality_id != self.appointment_form.speciality_id
    ):
        speciality = await self.booking_provider.get_speciality_by_id(
            doctor.speciality_id
        )
        self.appointment_form.medecin = doctor.name
        prompt, tools = self.prompts.ask_doctor_name(
            doctors,
            doctor,
            specific_speciality=speciality,
        )
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            prompt,
            tools,
        )
        return

    if self.appointment_form.medecin != doctor.name:
        self.appointment_form.medecin = doctor.name
        prompt, tools = self.prompts.ask_doctor_name(doctors, doctor)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if doctor.practitioner_id:
        await self._say_and_wait(
            llm,
            context,
            f"Parfait",
        )

    self.appointment_form.medecin = doctor.name
    self.appointment_form.practitioner_id = doctor.practitioner_id
    self.appointment_form.agenda_id = doctor.agenda_id
    self.appointment_form.speciality_id = doctor.speciality_id

    if self.main_motive and self.main_motive.speciality_id != doctor.speciality_id:
        if self.main_motive_per_speciality.get(doctor.speciality_id):
            self.main_motive = find_motive_by_id(
                self.main_motive_per_speciality[doctor.speciality_id],
                self.company_data,
            )
        else:
            self.main_motive = None

    self.main_motive = await get_main_motive(
        self._patient_data,
        self.main_motive,
        self.current_to_premieres_consultations,
        self.adult_to_enfants_consultations,
        self.company_data,
        medecin=self.appointment_form.medecin,
    )

    if self.appointment_form.visit_motive_id:
        prompt, tools = self.prompts.ask_confirm_reason_motive(
            self.appointment_form.visit_motive_name,
            intent=self.phone_caller.intent,
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    prompt, tools = self.prompts.ask_motive(
        self.main_motive, return_node=AppointmentFormTools.handle_doctor_name
    )
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
