from typing import TYPE_CHECKING

from ..constants import AVA<PERSON>ABILITIES_CHECK_INTRO_SENTENCE

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
import utils.appointments
from datetime import datetime
from constants import (
    DAYS_OF_THE_WEEK,
    MAX_APPOINTMENT_DAYS,
    MAX_RETRIES_TAKE_APPOINTMENT,
)
from models import MotiveAppointment
import pytz
from dateutil.parser import parse
from ..tools import AppointmentFormTools


async def handle_appointment_day(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    args_appointment_day = args.get("day").lower() if args.get("day") else None
    args_appointment_date = str(args.get("date"))

    if args_appointment_date in ["other", "autre"] or args_appointment_day in [
        "other",
        "autre",
    ]:
        prompt, tools = self.prompts.suggest_a_day(already_asked=True)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if (
        not args_appointment_date
        and args_appointment_day not in DAYS_OF_THE_WEEK.__members__.keys()
    ):
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Désolé, je n'ai pas compris le jour. Pouvez-vous répéter? Merci de me donner une date ou un jour de la semaine.",
            [AppointmentFormTools.handle_appointment_day],
        )
        return

    if args_appointment_day and args_appointment_date:
        args_appointment_day = None

    if self.retries_take_appointment > MAX_RETRIES_TAKE_APPOINTMENT:
        self.phone_caller.has_note = True
        await self._say_and_wait(
            llm,
            context,
            "Je suis désolé, je n'ai pas trouvé de jour qui vous convenez. Je vais informer le centre médical de votre demande, vous serez recontacté sous peu de temps.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback)
        return

    # find the next available date for this day
    wish_next_appointment_date = None
    if (
        args_appointment_date
        and isinstance(args_appointment_date, str)
        and args_appointment_date.strip()
    ):
        try:
            args_appointment_date = args_appointment_date.strip()
            wish_next_appointment_date = parse(args_appointment_date, fuzzy=True)
        except Exception:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris la date. Pouvez-vous répéter avec une date valide ?",
                [AppointmentFormTools.handle_appointment_day],
            )
            return

    if args_appointment_day and not args_appointment_date:
        wish_next_appointment_date = utils.appointments.get_date_from_day(
            args_appointment_day
        )

    if not args_appointment_day and not args_appointment_date:
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Désolé, je n'ai pas compris votre demande. Pouvez-vous répéter?",
            [AppointmentFormTools.handle_appointment_day],
        )
        return


    msg = f"{AVAILABILITIES_CHECK_INTRO_SENTENCE} pour votre {self.appointment_form.visit_motive_name}." \
        if self.appointment_form.number_of_motives == 1 \
        else f"{AVAILABILITIES_CHECK_INTRO_SENTENCE} pour vos rendez-vous."
    await self._say_and_wait(
        llm,
        context,
        message=msg,
    )

    _next_availabilities = await self.booking_provider.get_next_availabilities(
        MotiveAppointment(
            visit_motive_id=self.appointment_form.visit_motive_id,
            visit_motive_name=self.appointment_form.visit_motive_name,
            open=self.appointment_form.is_open_motive,
        ),
        self.agenda_ids_to_check,
        from_date=wish_next_appointment_date,
        min_time_difference=None,
        number_of_time_slots=None,
        steps_motives=self.appointment_form.steps_motives,
    )

    if not _next_availabilities or len(_next_availabilities) == 0:
        self.retries_take_appointment += 1
        prompt, tools = self.prompts.suggest_a_day(
            already_asked=True,
            first_sentence=f"Je ne trouve rien pour le {wish_next_appointment_date}.",
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    self.next_availabilities += _next_availabilities

    seen = set()
    unique_appointments = []
    for appointment in self.next_availabilities:
        if appointment.start_date not in seen:
            seen.add(appointment.start_date)
            unique_appointments.append(appointment)

    self.next_availabilities = unique_appointments

    next_datetime = (
        self.next_availabilities[0].start_date
        if len(self.next_availabilities) > 0
        else None
    )

    current_datetime = datetime.now(pytz.UTC)
    days_difference = (
        (next_datetime - current_datetime).days
        if next_datetime
        else MAX_APPOINTMENT_DAYS + 1
    )

    if days_difference > MAX_APPOINTMENT_DAYS:
        await self._say_and_wait(
            llm,
            context,
            "Je suis désolé mais il n'y a pas de rendez-vous disponible actuellement. Je vous invite à rappeler plus tard de nouveaux créneaux ouvrent chaque jour.",
        )
        self.phone_caller.successful_call = True
        await self.end_call("end_call", None, args, llm, context, result_callback)
        return

    find_appointments = utils.appointments.get_different_time_slots(
        _next_availabilities, 4, 2
    )
    if not find_appointments or len(find_appointments) == 0:
        find_appointments = utils.appointments.get_different_time_slots(
            _next_availabilities, 2, 2
        )

    if not find_appointments or len(find_appointments) == 0:
        datetime_str = utils.appointments.format_datetime_with_day_in_french(
            wish_next_appointment_date
        ).split("(")[0]

        self.retries_take_appointment += 1
        prompt, tools = self.prompts.suggest_a_day(
            already_asked=True,
            first_sentence=f"Il n'y a pas de disponibilité pour le {datetime_str}.",
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    prompt, tools = self.prompts.suggest_appointment_datetime(
        find_appointments, self.say_name_doctor
    )
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
