from typing import TYPE_CHECKING

from ..utils.imaging import check_is_mri_or_scanner

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

import utils.appointments
from models import BookingProviderType
from constants import AGE_CATEGORIES, MAX_RETRIES_TAKE_APPOINTMENT
from ..node_functions import get_main_motive, get_agendas_ids_to_check, get_adult_motive
from ..tools import AppointmentFormTools
from utils import remove_accents


async def handle_motive(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    reason_motive = str(args.get("reason_motive"))

    """
    START CUSTOM
    """

    if self.company_data.config in ["config37", "config143"] and check_is_mri_or_scanner(reason_motive):
        args["forward_number"] = "+33160828800"
        await self.forward_call(
            "forward_call", None, args, llm, context, result_callback
        )
        return

    """
    END CUSTOM
    """

    if self.has_confirm_motive:
        print("Already confirmed motive, skipping handle_motive.")
        return

    is_main_reason = (
        ("oui" in str(reason_motive).lower() and self.main_motive)
        or "nouveau" == str(reason_motive).lower()
        or self.main_motive
        and (
            self.main_motive.visit_motive_name == reason_motive
            or reason_motive.lower() in self.main_motive.visit_motive_name.lower()
        )
    )

    if "non" in str(reason_motive).lower() or "autre" in str(reason_motive).lower():
        self.main_motive = None
        prompt, tools = self.prompts.ask_motive(words_boost=self.words_boost)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    found_motive = self.main_motive

    if not is_main_reason:

        reason_motive = utils.remove_accents(reason_motive)
        motive_name = str(reason_motive).lower()
        print("-------")
        print(self.appointment_form.type)
        print(self._patient_data.is_new_patient)

        if self._patient_data.is_new_patient and "suivi" not in motive_name:
            motive_name = " première " + motive_name

        if self.appointment_form.type:
            motive_name += f" est le 'visit_motive_name'. Le 'type' devrait être {self.appointment_form.type} "

        motive_name = (
            "enfant " + motive_name
            if self._patient_data.age_category == AGE_CATEGORIES.ENFANT.value
            else motive_name
        )

        found_motive = await self.booking_provider.get_visit_motive_by_name_or_id(
            motive_name,
            medecin=(
                self.appointment_form.medecin if self.appointment_form.medecin else None
            ),
        )

        if (
            not found_motive
            and self.retries_take_appointment < MAX_RETRIES_TAKE_APPOINTMENT
        ):
            self.retries_take_appointment += 1
            self.appointment_form.visit_motive_id = None
            prompt, tools = self.prompts.ask_motive(
                first_sentence=f"Désolé, je n'ai pas trouvé le motif {reason_motive}, pouvez-vous répéter?",
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if (
            not found_motive
            and self.retries_take_appointment > MAX_RETRIES_TAKE_APPOINTMENT
        ):
            await self._say_and_wait(
                llm,
                context,
                f"Désolé, je n'ai pas trouvé le motif {reason_motive}.",
            )
            await self.forward_call(
                "forward_call", None, args, llm, context, result_callback
            )
            return

        if (
            self._patient_data.is_new_patient
            or self._patient_data.age_category == AGE_CATEGORIES.ENFANT.value
        ):
            found_motive = await get_main_motive(
                self._patient_data,
                found_motive,
                self.current_to_premieres_consultations,
                self.adult_to_enfants_consultations,
                self.company_data,
                arg_motive=reason_motive,
            )

        print("Found motive:", found_motive)
        print(self._patient_data.age_category)
        print(self.adult_to_enfants_consultations.values())

        flatten_adult_to_enfants_consultations = (
            [
                item
                for sublist in self.adult_to_enfants_consultations.values()
                for item in sublist
            ]
            if self.adult_to_enfants_consultations
            else []
        )

        if (
            found_motive
            and found_motive.visit_motive_id in flatten_adult_to_enfants_consultations
            and self._patient_data.age_category == AGE_CATEGORIES.ADULT.value
        ):
            adult_motive = get_adult_motive(
                self.adult_to_enfants_consultations,
                found_motive.visit_motive_id,
                self.company_data,
            )

            if adult_motive:
                found_motive = adult_motive

    if not found_motive:
        await self._say_and_wait(
            llm,
            context,
            f"Désolé, je n'ai pas trouvé le motif {reason_motive}.",
        )
        await self.forward_call(
            "forward_call", None, args, llm, context, result_callback
        )
        return

    """
    Start Handle agenda_ids for the appointment
    """
    print(found_motive)

    # Default agenda IDs
    self.agenda_ids_to_check = get_agendas_ids_to_check(
        self.company_data,
        self.phone_caller,
        self.appointment_form,
        found_motive,
        self._patient_data,
        self.ask_doctor_name,
    )
    print(self.agenda_ids_to_check)

    if len(self.agenda_ids_to_check) == 1:
        doctors = await self.booking_provider.get_doctors()
        doctor = utils.appointments.get_doctor_by_agenda_id(
            self.agenda_ids_to_check[0],
            doctors,
        )

        if (
            found_motive.speciality_id
            and found_motive.speciality_id != doctor.speciality_id
        ):
            self.main_motive = found_motive
            await self._say_and_wait(
                llm,
                context,
                f"Je suis désolé, le docteur {doctor.name.lower()} ne peut pas vous prendre en charge pour ce motif.",
                no_wait=True,
            )

            context.add_message(
                {
                    "role": "system",
                    "content": f"La spécialité du motif {found_motive.visit_motive_name} ne peux que être pris par les docteurs qui dont du {found_motive.type}",
                }
            )

            doctor_speciality = self._patient_data.historical_doctors.get(
                found_motive.speciality_id
            )
            doctors = await self.booking_provider.get_doctors()
            prompt, tools = self.prompts.ask_doctor_name(doctors, doctor_speciality)
            self.main_motive = found_motive
            if doctor_speciality:
                self.appointment_form.medecin = doctor_speciality.name
                self.appointment_form.practitioner_id = (
                    doctor_speciality.practitioner_id
                )
                self.appointment_form.agenda_id = doctor_speciality.agenda_id
            else:
                self.appointment_form.medecin = None
                self.appointment_form.practitioner_id = None
                self.appointment_form.agenda_id = None
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    """
    END Handle agenda_ids for the appointment
    """

    self.appointment_form.visit_motive_id = found_motive.visit_motive_id
    self.appointment_form.is_open_motive = found_motive.open
    self.appointment_form.visit_motive_name = found_motive.visit_motive_name
    self.appointment_form.instructions = found_motive.instructions
    self.appointment_form.speciality_id = found_motive.speciality_id

    # Pour EDL, le champ medecin contient le code du motif (ex: V01RXGEN)
    if (
        self.company_data.booking_provider == BookingProviderType.EDL
        and found_motive.medecin
    ):
        self.appointment_form.medecin = found_motive.medecin

    # Jump directly to the appointment datetime
    if is_main_reason:
        await self.handle_confirm_motive(
            "handle_confirm_motive",
            None,
            {"confirm": True},
            llm,
            context,
            result_callback,
        )
        return

    prompt, tools = self.prompts.ask_confirm_reason_motive(
        self.appointment_form.visit_motive_name,
        intent=self.phone_caller.intent,
        return_node=AppointmentFormTools.handle_motive,
    )
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
