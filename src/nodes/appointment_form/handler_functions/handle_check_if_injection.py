from typing import TYPE_CHECKING

from nodes.appointment_form.node_functions import find_motive_by_id
from nodes.appointment_form.utils.imaging import find_motives_with_injection_option

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)


async def handle_check_if_injection(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    has_injection = bool(args.get("has_injection", False))

    motive = find_motive_by_id(self.appointment_form.visit_motive_id, self.company_data)
    motives_with_injection_option = find_motives_with_injection_option(motive, self.company_data)
    motive_with_injection = motives_with_injection_option.with_injection
    motive_without_injection = motives_with_injection_option.without_injection

    if has_injection and motive_with_injection:
        self.appointment_form.visit_motive_id = motive_with_injection.visit_motive_id
        self.appointment_form.visit_motive_name = motive_with_injection.visit_motive_name
        self.appointment_form.is_open_motive = motive_with_injection.open
        self.appointment_form.instructions = motive_with_injection.instructions
    elif not has_injection and motive_without_injection:
        self.appointment_form.visit_motive_id = motive_without_injection.visit_motive_id
        self.appointment_form.visit_motive_name = motive_without_injection.visit_motive_name
        self.appointment_form.is_open_motive = motive_without_injection.open
        self.appointment_form.instructions = motive_without_injection.instructions
    else:
        raise ValueError(f"No motive found for motive {self.appointment_form.visit_motive_name} with injection option: {has_injection}")

    await self.handle_confirm_motive(
        function_name,
        tool_call_id,
        {
            "confirm": True,
            "has_injection": has_injection,
            "skip_has_injection_check": True,
        },
        llm,
        context,
        result_callback,
    )
