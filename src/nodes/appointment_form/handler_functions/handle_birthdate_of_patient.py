from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from constants import (
    AGE_CATEGORIES,
    ADULT_AGE,
)
from datetime import datetime
from ..node_functions import (
    get_main_motive,
)

import utils.appointments
from nodes.appointment_form.tools import AppointmentFormTools


async def handle_birthdate_of_patient(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    birthdate = args.get("birthdate")

    if not utils.appointments.is_valid_date(birthdate):
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Dit juste: 'Désolé, je n'ai pas compris votre date de naissance. Pouvez-vous répéter?'",
            [AppointmentFormTools.handle_birthdate_of_patient],
        )
        return

    if isinstance(birthdate, str):
        try:
            birthdate = datetime.strptime(birthdate, "%Y-%m-%d")
        except ValueError:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                f"J'ai compris que la date de naissance est '{birthdate}', est-ce correct? ",
                [AppointmentFormTools.handle_birthdate_of_patient],
            )
            return

    self._patient_data.birthdate = birthdate
    self._patient_data.age_category = (
        AGE_CATEGORIES.ADULT.value
        if utils.appointments.is_minimum_age(birthdate, ADULT_AGE)
        else AGE_CATEGORIES.ENFANT.value
    )

    if not utils.appointments.is_minimum_age(birthdate, self.minimum_age):
        min_age_str = str(self.minimum_age)
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, le patient doit avoir au moins {min_age_str} ans pour prendre rendez-vous.",
        )
        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        await self.end_call("end_call", None, args, llm, context, result_callback)
        return

    if not utils.appointments.is_maximum_age(birthdate, self.maximum_age):
        max_age_str = str(self.maximum_age)
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, le patient doit avoir moins de {max_age_str} ans pour prendre rendez-vous.",
        )
        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        await self.end_call("end_call", None, args, llm, context, result_callback)
        return

    if (
        self._patient_data.is_new_patient
        or self._patient_data.age_category == AGE_CATEGORIES.ENFANT.value
    ):
        self.main_motive = await get_main_motive(
            self._patient_data,
            self.main_motive,
            self.current_to_premieres_consultations,
            self.adult_to_enfants_consultations,
            self.company_data,
        )

    if self.ask_doctor_name:
        doctors = await self.booking_provider.get_doctors(self._patient_data)
        main_doctor = self._patient_data.medecin_historique
        self.appointment_form.medecin = main_doctor.name if main_doctor else None

        if not doctors:
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, il n'y a pas de médecins disponibles pour le moment.",
            )
            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            await self.end_call(
                "end_call",
                None,
                args,
                llm,
                context,
                result_callback,
                ask_last_question=True,
            )
            return

        if len(doctors) == 1:
            self.appointment_form.medecin = doctors[0].name
            await self.handle_doctor_name(
                "handle_doctor_name",
                None,
                {"doctor_name": doctors[0].name.lower()},
                llm,
                context,
                result_callback,
            )
            return

   
        prompt, tools = self.prompts.ask_doctor_name(
            doctors, main_doctor
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    sentence = (
        "Quel est le premier examen souhaité?"
        if self.appointment_form.number_of_motives == 2
        else None
    )

    prompt, tools = self.prompts.ask_motive(
        self.main_motive, first_sentence=sentence, words_boost=self.words_boost
    )
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
