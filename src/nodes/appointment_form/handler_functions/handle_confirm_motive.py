from typing import TYPE_CHECKING

from ..utils.imaging import (
    check_forbidden_motives_combination,
    check_is_mri,
    check_has_injection,
    check_is_mri_or_scanner,
    check_if_motive_has_injection_option,
)

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from constants import MAX_RETRIES_TAKE_APPOINTMENT, Intents, GENDER, METADATA
import utils.appointments
from ..node_functions import (
    find_motive_by_id,
    find_motive_by_name,
    get_next_availabilties,
)
import utils.tts
from models import MotiveAppointment
from datetime import datetime
from datetime import timedelta
from ..tools import AppointmentFormTools
from loguru import logger


async def handle_confirm_motive(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    if self.has_confirm_motive:
        if self.next_availabilities:
            prompt, tools = self.prompts.suggest_appointment_datetime(
                [],
                with_doctor=self.company_data.bot_configuration.say_name_doctor,
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        prompt, tools = self.prompts.suggest_appointment_datetime([])
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    confirm = args.get("confirm")
    with_mammaire = args.get("with_mammaire", False)
    has_done_condition = args.get("has_done_condition", False)
    confirm = confirm == True or "oui" in str(confirm).lower()

    print(self.appointment_form.visit_motive_id)
    print(self.appointment_form.visit_motive_name)
    print(self.appointment_form.is_open_motive)
    # the user has already confirmed the reason
    if self.appointment_form.start_date:
        confirm = True

    if self.retries_take_appointment > MAX_RETRIES_TAKE_APPOINTMENT:
        await self._say_and_wait(
            llm,
            context,
            "Je suis désolé, je n'ai pas trouvé le motif que vous souhaitez",
        )
        await self.forward_call(
            "forward_call",
            None,
            args,
            llm,
            context,
            result_callback,
        )
        return

    if not confirm:
        self.retries_take_appointment += 1
        if self.phone_caller.intent == Intents.MODIFIER:
            await self._say_and_wait(
                llm,
                context,
                "Si vous ne souhaitez plus votre rendez-vous, vous pouvez l'annuler avec un autre appel. Je vais maintenant vous prendre un nouveau rendez-vous.",
            )
            self.phone_caller.intent = Intents.NOUVEAU
            self.appointment_form.reset()

        prompt, tools = self.prompts.ask_motive()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if self.motif_alias:
        motif_map = self.motif_alias.get(self.company_data.config, {})
        self.appointment_form.visit_motive_id = motif_map.get(
            self.appointment_form.visit_motive_id,
            self.appointment_form.visit_motive_id,
        )

    found_motive = find_motive_by_id(
        self.appointment_form.visit_motive_id,
        self.company_data,
        self.appointment_form.medecin,
    )

    if (
        self.company_data.config == "config92"
        and "(tous)" in found_motive.visit_motive_name
        and self.ask_doctor_name
        and self.appointment_form.medecin
    ):
        found_motive = find_motive_by_name(
            found_motive.visit_motive_name.replace("(tous)", "").strip(),
            self.company_data,
            doctor_name=self.appointment_form.medecin,
        )
        print(
            f"Found motive by name: {found_motive.visit_motive_name} ({found_motive.visit_motive_id}) for doctor {self.appointment_form.medecin}"
        )
        self.appointment_form.visit_motive_id = found_motive.visit_motive_id
        self.appointment_form.speciality_id = found_motive.speciality_id
        self.appointment_form.visit_motive_name = found_motive.visit_motive_name
        self.appointment_form.is_open_motive = found_motive.open
        self.appointment_form.instructions = found_motive.instructions

    if not found_motive:
        prompt, tools = self.prompts.ask_motive()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if not utils.appointments.is_minimum_age(
        self._patient_data.birthdate, found_motive.age_minimum
    ):
        age_str = str(int(found_motive.age_minimum))
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, le patient doit avoir au moins {age_str} ans pour prendre rendez-vous.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback, True)
        return

    if not utils.appointments.is_maximum_age(
        self._patient_data.birthdate, found_motive.age_maximum
    ):
        age_str = str(int(found_motive.age_maximum))
        await self._say_and_wait(
            llm,
            context,
            f"Je suis désolé, seul les patients de moins de {age_str} ans peuvent prendre rendez-vous.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback, True)
        return

    if (
        METADATA.female.value in found_motive.metadata
        and self._patient_data.gender == GENDER.MALE
    ):
        await self._say_and_wait(
            llm,
            context,
            "Ce motif n'est pas disponible pour les hommes.",
            no_wait=True,
        )
        prompt, tools = self.prompts.ask_motive()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if self.company_data.config in [
        "config88",
        "config108",
    ] and found_motive.visit_motive_id in [135296, 3148777]:
        await self._say_and_wait(
            llm,
            context,
            "Malheureusement, nous ne pouvons pas fixer de rendez-vous pour ce motif pour le moment.",
        )
        self.phone_caller.has_note = False
        await self.end_call("end_call", None, args, llm, context, result_callback, True)
        return

    if self.should_transfer_motive and self.should_transfer_motive(
        self.company_data.config,
        found_motive,
        self._patient_data,
        self.phone_caller,
    ):
        await self.forward_call(
            "forward_call", None, args, llm, context, result_callback
        )
        return

    if self.company_data.config == "config87" and found_motive.visit_motive_id in [
        249043
    ]:
        email = utils.tts.convert_email_to_spoken_text("<EMAIL>")
        message = f"Merci d'envoyer un mail à {email} avec vos coordonnées en demandant une consultation de suivi"
        await self._say_and_wait(
            llm,
            context,
            message,
        )

    if self.company_data.config == "config87" and found_motive.speciality_id == 6:
        medecin_historique = self._patient_data.historical_doctors.get(
            found_motive.speciality_id, None
        )
        if self._patient_data.is_new_patient or (
            medecin_historique is None or medecin_historique.agenda_id != 405464
        ):
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, mais nous ne pouvons pas prendre de rendez-vous pour les nouveaux patients dans cette spécialité.",
            )
            await self.end_call(
                "end_call", None, args, llm, context, result_callback, True
            )
            return

    if not self.appointment_form.is_open_motive:
        if (
            self.appointment_form.instructions
            and len(self.appointment_form.instructions.strip()) > 0
        ):
            await self._say_and_wait(
                llm,
                context,
                f"{self.appointment_form.instructions}",
            )

        self.phone_caller.has_note = True
        self.phone_caller.successful_call = True
        await self._say_and_wait(
            llm,
            context,
            "Pour ce type de demande, il n'est malheureusement pas possible de finaliser la prise de rendez-vous par téléphone. Votre demande a bien été prise en compte, et un membre de notre équipe d'accueil vous recontactera dans les plus brefs délais.",
            no_wait=True,
        )

        if not self._patient_data.has_completed_form():
            prompt, tools = self.prompts.contact_form_prompts.ask_first_name()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        elif self.ask_doctor_name and self.appointment_form.medecin:
            await self._say_and_wait(
                llm,
                context,
                f"Je notifie Docteur {self.appointment_form.medecin} de votre demande.",
            )
            await self.end_call(
                "end_call", None, args, llm, context, result_callback, True
            )
            return

        else:
            self.phone_caller.has_note = True

            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

    if (
        with_mammaire == False
        and "mamm" in str(self.appointment_form.visit_motive_name).lower()
    ):
        prompt, tools = self.prompts.ask_with_echo_mammaire(found_motive)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if (
        has_done_condition == False
        and found_motive.visit_motive_id in self.motives_with_condition.keys()
    ):
        prompt, tools = self.prompts.ask_motive_condition(
            self.motives_with_condition.get(found_motive.visit_motive_id)
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if (
        self.company_data.config in ["config82"] and found_motive.speciality_id in [1]
    ) or (
        self.company_data.config in ["config87"]
        and found_motive.visit_motive_id in [249045, 374200, 249042]
    ):
        await self._say_and_wait(
            llm,
            context,
            "Nous vous informons qu'en raison d'une forte demande, les plages de cette consultation sont actuellement très limitées. Si aucun créneau ne peut être proposé au cours de cet appel, nous vous recommandons de consulter régulièrement Doctolib, où les disponibilités libérées sont immédiatement réaffichées.",
        )

    """ 
    END CUSTOM
    """

    # Check if we need to check for injection prescription
    skip_has_injection_check = args.get("skip_has_injection_check", False)
    if not skip_has_injection_check and check_if_motive_has_injection_option(
        found_motive, self.company_data
    ):
        prompt, tools = self.prompts.ask_if_injection()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    # Check for injection allergies
    # TODO: can be merged with ask_motive_condition
    has_injection = args.get("has_injection", False)
    skip_injection_allergies_check = args.get("skip_injection_allergies_check", False)
    if not skip_injection_allergies_check and (
        has_injection or check_has_injection(found_motive.visit_motive_name)
    ):
        prompt, tools = self.prompts.ask_for_injection_allergies()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    # Check for MRI counter indicat ions
    # TODO: can be merged with ask_motive_condition
    skip_mri_check = args.get("skip_mri_check", False)
    if not skip_mri_check and check_is_mri(found_motive.visit_motive_name):
        print(
            f"Checking MRI counter indications for motive {found_motive.visit_motive_name}"
        )
        prompt, tools = self.prompts.ask_for_mri_counter_indications()
        print(f"tools : {tools}")
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if (
        self.appointment_form.instructions
        and len(self.appointment_form.instructions.strip()) > 0
    ):
        await self._say_and_wait(
            llm,
            context,
            f"{self.appointment_form.instructions}",
            no_wait=True,
        )

    if (
        self.appointment_form.number_of_motives > 1
        and len(self.appointment_form.steps_motives)
        < self.appointment_form.number_of_motives
    ):

        # make sure that there is no two similarly named forbidden motives in the same day
        if len(self.appointment_form.steps_motives) > 0:
            forbidden_motive = check_forbidden_motives_combination(
                self.appointment_form.steps_motives[0].visit_motive_name,
                found_motive.visit_motive_name,
            )
            if forbidden_motive:
                await self._say_and_wait(
                    llm,
                    context,
                    f"Il n'est malheureusement pas possible de réaliser {forbidden_motive} le même jour. Nous allons donc vous transférer vers le secrétariat afin qu'un membre de l'accueil puisse vous aider à planifier ces deux rendez-vous sur des créneaux distincts.",
                )
                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
                return

        self.appointment_form.steps_motives.append(found_motive)
        remaining = self.appointment_form.number_of_motives - len(
            self.appointment_form.steps_motives
        )
        if remaining > 0:
            # TODO: ajouter la redemande de specialité pour le dexieme examen
            prompt, tools = self.prompts.ask_motive(
                None,
                None,
                first_sentence="Quel est le second examen souhaité?",
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    if self.company_data.config in ["config87"] and found_motive.visit_motive_id in [
        114253
    ]:
        self.appointment_form.number_of_motives = 2
        ecg_motive = find_motive_by_id(220852, self.company_data)
        self.appointment_form.steps_motives = [
            ecg_motive,
            found_motive,
        ]

    # clichy for Dr DOMNGANG
    if self.company_data.config in ["config91"] and found_motive.visit_motive_id in [
        356192,
        356196,
        107717,
    ]:
        self.agenda_ids_to_check = [68102]
        self.appointment_form.agenda_id = 68102
        self.appointment_form.medecin = "DOMNGANG Olivier"
        self.appointment_form.practitioner_id = 191
        logger.info(
            f"Setting agenda_id to {self.appointment_form.agenda_id} for motive {found_motive.visit_motive_name}"
        )

    if self.phone_caller.intent in [
        Intents.MODIFIER,
        Intents.ANNULATION,
        Intents.CONFIRMER,
    ]:
        self.agenda_ids_to_check = [self.old_appointment_form.agenda_id]

    wish_appointment_date = datetime.now().date()

    await self._say_and_wait(
        llm,
        context,
        f"Un instant. Je prends l'agenda pour trouver la prochaine disponibilité pour votre {self.appointment_form.visit_motive_name}",
    )

    self.next_availabilities = await get_next_availabilties(
        wish_appointment_date,
        self.next_availabilities,
        self.booking_provider,
        self.appointment_form,
        agenda_ids_to_check=self.agenda_ids_to_check,
        company_data=self.company_data,
        patient_data=self._patient_data,
        phone_caller=self.phone_caller,
    )

    if (
        (not self.next_availabilities or len(self.next_availabilities) == 0)
        and len(self.agenda_ids_to_check)
        and not self.ask_doctor_name
    ):
        if len(self.agenda_ids_to_check) == 1:
            doctor = await self.booking_provider.get_doctor_by_agenda_id(
                self.agenda_ids_to_check[0]
            )
            if doctor:
                await self._say_and_wait(
                    llm,
                    context,
                    f"Le Docteur {doctor.name.lower().split('(')[0].strip()} n'a pas de créneaux disponibles pour le moment. Je vais vérifier les autres médecins disponibles.",
                )

        doctors = await self.booking_provider.get_doctors_by_speciality(
            found_motive.speciality_id
        )
        if len(doctors) > 0:
            self.agenda_ids_to_check = [
                doctor.agenda_id for doctor in doctors if doctor.agenda_id
            ]

            self.next_availabilities = await get_next_availabilties(
                wish_appointment_date,
                self.next_availabilities,
                self.booking_provider,
                self.appointment_form,
                agenda_ids_to_check=self.agenda_ids_to_check,
                company_data=self.company_data,
                patient_data=self._patient_data,
                phone_caller=self.phone_caller,
            )

    if (not self.next_availabilities) or len(self.next_availabilities) == 0:
        if (
            self.company_data.bot_configuration.ask_name_doctor
            and self.appointment_form.medecin
            and self.appointment_form.agenda_id
            and not self.has_asked_to_change_doctor
        ):
            doctor = await self.booking_provider.get_doctor_by_agenda_id(
                self.appointment_form.agenda_id
            )
            if doctor:
                prompt, tools = self.prompts.ask_continue_with_same_doctor(doctor)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        self.phone_caller.successful_call = True
        await self._say_and_wait(
            llm,
            context,
            "Je suis désolé mais il n'y a pas de rendez-vous disponible actuellement. Je vous invite à rappeler plus tard de nouveaux créneaux ouvrent chaque jour.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback, True)
        return

    next_availabilities = utils.appointments.get_different_time_slots(
        self.next_availabilities, 4, 2
    )

    if len(next_availabilities) == 1:
        next_availabilities = utils.appointments.get_different_time_slots(
            self.next_availabilities, 2, 2
        )

    self.suggested_appointments = next_availabilities
    prompt, tools = self.prompts.suggest_appointment_datetime(
        next_availabilities,
        with_doctor=self.company_data.bot_configuration.say_name_doctor,
    )
    self.has_confirm_motive = True
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
    return
