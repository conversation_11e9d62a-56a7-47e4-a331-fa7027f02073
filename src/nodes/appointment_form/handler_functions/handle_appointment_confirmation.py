from loguru import logger
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from constants import Intents
import utils.tts
import traceback


async def handle_appointment_confirmation(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    confirm = args["confirm"]
    confirm = (
        confirm == True
        or "oui" in str(confirm).lower()
        or "confirm" in str(confirm).lower()
        or "yes" in str(confirm).lower()
    )
    try:
        if self.phone_caller.intent == Intents.NOUVEAU:
            if confirm:
                vos = "vos" if self.appointment_form.number_of_motives > 1 else "votre"
                if not self.appointment_form.is_booking_created:
                    await self._say_and_wait(
                        llm,
                        context,
                        f"Un instant, je réserve {vos} rendez-vous.",
                    )

                    self.appointment_form = (
                        await self.booking_provider.create_new_booking(
                            phone_caller=self.phone_caller,
                            patient_data=self._patient_data,
                            booking_info=self.appointment_form,
                        )
                    )

                self.phone_caller.successful_call = True
                await llm.push_frame(self.sounds["ding2.wav"])

                # Message adapté selon le nombre d'examens
                sont = "sont" if self.appointment_form.number_of_motives > 1 else "est"
                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message=f"{vos} rendez-vous {sont} confirmés.",
                )

                await self.output_function(
                    "output_function",
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return
            else:
                self.retries_take_appointment += 1
                self.has_confirm_datetime = False
                prompt, tools = self.prompts.suggest_a_day(already_asked=True)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        if self.phone_caller.intent == Intents.MODIFIER:
            if (
                confirm
                and self.appointment_form.start_date
                and self.old_appointment_form
            ):
                await self._say_and_wait(
                    llm,
                    context,
                    "Un instant, je modifie votre rendez-vous.",
                )

                await self.booking_provider.modify_appointment(
                    self.phone_caller,
                    self._patient_data,
                    self.appointment_form,
                    self.old_appointment_form,
                )

                self.phone_caller.successful_call = True
                await llm.push_frame(self.sounds["ding2.wav"])

                intent = utils.tts.get_intent_description(self.phone_caller.intent)
                await self._say_and_wait(llm, context, f"Votre {intent} est confirmé.")
                await self.output_function(
                    "output_function",
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

        if self.phone_caller.intent == Intents.RETARD:
            await self.booking_provider.update_appointment_with_note(
                self.appointment_form, self.phrases["LATE_APPOINTMENT_NOTE"]
            )
            await self._say_and_wait(
                llm, context, self.phrases["LATE_APPOINTMENT_MESSAGE"]
            )

            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            if self.company_data.all_in_task:
                await self.output_function(
                    "output_function",
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return

            prompt, tools = self.prompts.ask_to_tranfer_call()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.ANNULATION:
            if confirm:
                await self.booking_provider.cancel_appointment(
                    self.appointment_form, self.phone_caller.intent
                )

                if (
                    self.appointment_form.visit_motive_id == 354572
                    and self.company_data.config == "config87"
                ):
                    # check that appointment after 354572 is 114253 or 114252
                    all_appointments = self._patient_data.appointments
                    appointment_date = self.appointment_form.start_date
                    second_appointment = next(
                        (
                            a
                            for a in all_appointments
                            if a.visit_motive_id in [114253, 114252]
                            and a.start_date.date() == appointment_date.date()
                        ),
                        None,
                    )
                    if second_appointment:
                        second_appointment.notes = self.appointment_form.notes
                        await self.booking_provider.cancel_appointment(
                            second_appointment, Intents.ANNULATION
                        )

                await llm.push_frame(self.sounds["ding2.wav"])
                vos = (
                    "vos"
                    if self.appointment_form.steps
                    and len(self.appointment_form.steps) > 1
                    else "votre"
                )
                sont = (
                    "sont"
                    if self.appointment_form.steps
                    and len(self.appointment_form.steps) > 1
                    else "est"
                )
                await self._say_and_wait(
                    llm, context, f"{vos} rendez-vous {sont} bien annulé"
                )
                await self.output_function(
                    "output_function",
                    tool_call_id,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return
            else:
                self.phone_caller.has_note = True
                prompt, tools = self.prompts.ask_last_question(
                    sentence="Quel rendez-vous souhaitez-vous annuler ? Je laisserai une note au secrétariat."
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        if self.phone_caller.intent == Intents.CONFIRMER:
            if confirm:
                await self.handle_confirm_appointment_metadata(
                    "handle_confirm_appointment_metadata",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                )
                return
            else:
                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
                return

        # Forward fall back
        await self.forward_call(
            "forward_call", tool_call_id, args, llm, context, result_callback
        )
    except Exception as e:
        logger.error(f"Error in handle_appointment_confirmation: {e}")
        await self._say_and_wait(
            llm,
            context,
            "Désolé, un problème est survenu lors de la prise de rendez-vous. Je vais laisser une note au secrétariat.",
        )
        self.phone_caller.has_note = True
        self.n8n_client.send_error_server(
            config=self.company_data.config,
            phone_caller=self.phone_caller,
            traceback=traceback.format_exc(),
        )
        await self.end_call("end_call", None, args, llm, context, result_callback)
        self.phone_caller.successful_call = False
        return
