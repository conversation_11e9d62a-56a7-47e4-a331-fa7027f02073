from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)
from rapidfuzz import fuzz
import utils
from nodes.appointment_form.tools import AppointmentFormTools
from nodes.appointment_form.node_functions import find_motive_by_id
from constants import Intents, ClientType


async def handle_how_many_motives(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    number_of_motives = int(args.get("number_of_motives"))

    if number_of_motives != 1 and number_of_motives != 2:
        message = "Dit: 'Merc<PERSON> de choisir entre 1 ou 2 examens.'"
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            message,
            [AppointmentFormTools.handle_how_many_motives],
        )
        return

    self.appointment_form.number_of_motives = int(number_of_motives)

    if int(number_of_motives) > 1 and self.company_data.client_type == ClientType.DEFAULT:
        self.phone_caller.successful_call = True
        await self.forward_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
        )
        return

    if not self._patient_data.birthdate:
        prompt, tools = self.prompts.ask_birthdate_of_patient()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return
    else:
        await self.handle_birthdate_of_patient(
            "handle_birthdate_of_patient",
            None,
            {"birthdate": self._patient_data.birthdate},
            llm,
            context,
            result_callback,
        )
        return
