from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)


async def handle_check_for_mri_counter_indications(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    confirm = args.get("confirm")
    has_counter_indications = confirm == True or "oui" in str(confirm).lower()
    if has_counter_indications:
        await self._say_and_wait(
            llm,
            context,
            "Pour votre sécurité, si vous portez un objet métallique, je ne suis pas habilité à effectuer la prise de rendez-vous pour une IRM. Pas d’inquiétude, je note votre demande et vous serez recontacté dans les plus brefs délais.",
            no_wait=True,
        )
        self.phone_caller.successful_call = True
        self.phone_caller.has_note = True
        await self.end_call(
            "end_call", None, args, llm, context, result_callback, True
        )
        return

    await self.handle_confirm_motive(
        function_name,
        tool_call_id,
        {
            "confirm": True,
            "skip_mri_check": True,
        },
        llm,
        context,
        result_callback,
    )
