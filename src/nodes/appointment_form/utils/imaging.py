from typing import Optional

from models import CompanyData, MotiveAppointment
from models.motive_appointments_with_injection_option import MotiveAppointmentWithInjectionOption

# Dict[str(copy), List[str(lookup)]]
FORBIDDEN_MOTIVES_COMBINATION_TEXT_LOOKUP = {
    "deux échographies": ["chographie", "chographie"],
    "une mammographie et une échographie": ["chographie", "mammo"],
    "deux IRM": ["irm", "irm"],
    "un IRM et un scanner": ["irm", "scanner"],
}

def check_forbidden_motives_combination(visit_motive_1: str, visit_motive_2: str) -> Optional[str]:
    for motive_copy, motive_lookups in FORBIDDEN_MOTIVES_COMBINATION_TEXT_LOOKUP.items():
        if motive_lookups[0] in visit_motive_1.lower() and motive_lookups[1] in visit_motive_2.lower():
            return motive_copy
        if motive_lookups[1] in visit_motive_1.lower() and motive_lookups[0] in visit_motive_2.lower():
            return motive_copy
    return None


def check_is_mri(visit_motive: str):
    return "irm" in visit_motive.lower()

def check_is_scanner(visit_motive: str):
    return "scanner" in visit_motive.lower()

def check_is_mri_or_scanner(visit_motive: str):
    return check_is_mri(visit_motive) or check_is_scanner(visit_motive)


WITH_INJECTION_OPTIONS = ["avec injection", "avec inj", "avec iv"]
WITHOUT_INJECTION_OPTIONS = ["sans injection", "sans inj", "sans iv"]
INJECTION_OPTIONS = WITH_INJECTION_OPTIONS + WITHOUT_INJECTION_OPTIONS

def check_has_injection(visit_motive: str):
    for injection_option in WITH_INJECTION_OPTIONS:
        if injection_option in visit_motive.lower():
            return True
    return False

# Strips injection option from motive name:
# Examples:
#   - Scanner avec injection -> scanner
#   - Whatever avec IV -> whatever
#   - IRM sans inj -> irm
def strip_injection_option_from_motive_name(motive_name: str):
    for injection_option in INJECTION_OPTIONS:
        motive_name = motive_name.lower().replace(injection_option, "")
    return motive_name.strip()


def find_motives_with_injection_option(
    motive: MotiveAppointment,
    company_data: CompanyData,
) -> MotiveAppointmentWithInjectionOption:
    motives = company_data.inbound_config_file.visit_motives_categories
    motive_base_name = strip_injection_option_from_motive_name(motive.visit_motive_name)

    similar_motives = []
    for item_motive in motives:
        item_motive_base_name = strip_injection_option_from_motive_name(item_motive.visit_motive_name)
        if motive_base_name == item_motive_base_name:
            similar_motives.append(item_motive)

    # We should only fetch 2 different motives max. Otherwise, there's a bug that we must address
    if len(similar_motives) > 2:
        raise ValueError(f"Found too many similar motives for injection options: {similar_motives}")

    # If we found exactly two similar motives, one is with injection and the other is without
    if len(similar_motives) == 2:
        if "avec" in similar_motives[0].visit_motive_name.lower() or "sans" in similar_motives[1].visit_motive_name.lower():
            return MotiveAppointmentWithInjectionOption(
                without_injection=similar_motives[1],
                with_injection=similar_motives[0]
            )
        return MotiveAppointmentWithInjectionOption(
            without_injection=similar_motives[0],
            with_injection=similar_motives[1]
        )


    # Else, we have only one motive, which should be the initial input motive
    if "avec" in motive.visit_motive_name.lower():
        return MotiveAppointmentWithInjectionOption(
            without_injection=None,
            with_injection=motive
        )

    return MotiveAppointmentWithInjectionOption(
        without_injection=motive,
        with_injection=None
    )

def check_if_motive_has_injection_option(motive: MotiveAppointment, company_data: CompanyData):
    if not check_is_mri_or_scanner(motive.visit_motive_name):
        return False
    motives_with_injection_option = find_motives_with_injection_option(motive, company_data)
    return motives_with_injection_option.with_injection is not None and motives_with_injection_option.without_injection is not None
