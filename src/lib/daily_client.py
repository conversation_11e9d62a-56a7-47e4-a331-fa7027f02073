from constants import MAX_SESSION_TIME, DAILY_DEV_ROOM
from pipecat.transports.services.helpers.daily_rest import (
    DailyRESTHelper,
    DailyRoomObject,
    DailyRoomProperties,
    DailyRoomSipParams,
    DailyRoomParams,
)
from fastapi import HTTPException
import subprocess
import os
import sys
import aiohttp
import requests
from typing import List
from loguru import logger
from customers.utils import is_client_exists

# Ensure Python can find 'customers'
base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
sys.path.append(base_path)


class DailyClient:

    def __init__(
        self,
        daily_api_key: str,
        daily_api_url: str,
        aiohttp_session: aiohttp.ClientSession,
    ):
        self.daily_helpers: DailyRESTHelper = DailyRESTHelper(
            daily_api_key=daily_api_key,
            daily_api_url=daily_api_url,
            aiohttp_session=aiohttp_session,
        )
        self.daily_api_key = daily_api_key
        self.daily_api_url = daily_api_url
        self.base_url = daily_api_url
        self.aiohttp_session = aiohttp_session

    async def start_customer_bot(
        self,
        customer: str,
        room: DailyRoomObject,
        callId: str,
        callDomain=None,
        config_id: str = None,
        caller_phone_number=None,
        token=None,
        speed_voice="1.15",
        is_test=False,
    ):
        """
        Starts the customer bot for a given call session.
        """
        if not is_client_exists(customer):
            logger.warning(
                f"Customer folder for {customer} does not exist. "
                "Using default bot configuration."
            )
            customer = "default"

        module_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "..", f"customers/{customer}"
        )

        if not os.path.exists(module_path):
            raise HTTPException(
                status_code=500, detail=f"Bot module not found: {module_path}"
            )

        # Convert customer directory to module notation
        module_name = "runner"
        # Construct the command
        bot_proc = [
            "python3",
            "-m",
            module_name,
            "--module_name",
            customer,
            "--url",
            room.url,
            "--token",
            token,
            "--call_id",
            callId,
            "--sip",
            room.config.sip_endpoint,
            "--speed_voice",
            speed_voice,
        ]

        if config_id is not None:
            bot_proc.extend(["--config_id", config_id])
        if caller_phone_number is not None:
            bot_proc.extend(["--phone_caller", caller_phone_number])
        if is_test:
            bot_proc.append("--test")

        try:
            subprocess.Popen(
                bot_proc,
                shell=False,
                cwd=base_path,
            )

        except Exception as e:
            logger.error(f"Failed to start subprocess: {e}")
            raise HTTPException(
                status_code=500, detail=f"Failed to start subprocess: {e}"
            )

    async def create_daily_room(self, exp: int = None) -> DailyRoomObject:
        """
        Creates a Daily room for a given call session.

        Args:
            room_url (str): URL of the Daily room to join.
            exp (int): Optional Unix epoch timestamp for room expiration (e.g., time.time() + 300 for 5 minutes)

        Returns:
            DailyRoomObject: Daily room object.
        """
        params = DailyRoomParams(
            properties=DailyRoomProperties(
                exp=exp,
                sip=DailyRoomSipParams(
                    display_name="dialin-user",
                    video=False,
                    sip_mode="dial-in",
                    num_endpoints=1,
                ),
            )
        )

        print("Creating new room...")
        return await self.daily_helpers.create_room(params=params)

    async def get_dev_daily_room(self) -> DailyRoomObject:
        """
        Gets the Daily room for the dev environment.

        Returns:
            DailyRoomObject: Daily room object.
        """
        room_url = DAILY_DEV_ROOM
        if not room_url:
            raise HTTPException(
                status_code=500, detail="Missing DAILY_DEV_ROOM environment variable"
            )
        try:
            room = await self.daily_helpers.get_room_from_url(room_url)
            if not room:
                raise HTTPException(
                    status_code=500, detail="Failed to get dev Daily room"
                )
            return room
        except Exception as e:
            print(e)

    async def create_daily_web_room(self, exp: int = None) -> DailyRoomObject:
        """
        Creates a Daily room for a web call session.

        Args:
            exp (int): Optional Unix epoch timestamp for room expiration (e.g., time.time() + 300 for 5 minutes)

        Returns:
            DailyRoomObject: Daily room object.
        """
        params = DailyRoomParams(
            properties=DailyRoomProperties(
                exp=exp,
                enable_prejoin_ui=False,
                enable_chat=True,
                start_video_off=True,
            )
        )

        print("Creating new web room...")
        return await self.daily_helpers.create_room(params=params)

    async def get_token(self, room: DailyRoomObject) -> str:
        """
        Gets a token for a given Daily room.

        Args:
            room_url (str): URL of the Daily room.

        Returns:
            str: Token for the room.
        """
        return await self.daily_helpers.get_token(room.url, MAX_SESSION_TIME)

    async def get_participant_ids(self, room_name: str) -> List[str]:
        """
        Gets the participant IDs in a Daily room.

        Args:
            room (DailyRoomObject): Daily room object.

        Returns:
            list: List of participant IDs.
        """
        result = requests.get(
            f"{self.base_url}/rooms/{room_name}/presence",
            headers={"Authorization": f"Bearer {self.daily_api_key}"},
        )
        print(f"Getting participant IDs: {result.status_code}")
        result.raise_for_status()
        return [participant["id"] for participant in result.json()["data"]]

    async def end_call(self, room_url: str):
        """
        Ends the call session.

        Args:
            room (DailyRoomObject): Daily room object.
        """
        room_name = room_url.split("/")[-1]
        participant_ids = await self.get_participant_ids(room_name)

        if len(participant_ids) == 0:
            print("No participants to eject")
            return

        print(f"Ejecting participants: {participant_ids}")
        result = requests.post(
            f"{self.base_url}/rooms/{room_name}/eject",
            headers={"Authorization": f"Bearer {self.daily_api_key}"},
            json={"ids": participant_ids},
        )
        print(f"Ending call: {result.status_code}")
        result.raise_for_status()
        await self.aiohttp_session.close()

    async def start_test_bot(
        self,
        room: DailyRoomObject,
        prompt_id: int = 1,
    ):
        """
        Starts the test bot for a given call session.
        """

        module_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "..", "test"
        )

        if not os.path.exists(module_path):
            raise HTTPException(
                status_code=500, detail=f"Bot module not found: {module_path}"
            )

        module_name = "test.runner"

        # Construct the command
        bot_proc = [
            "python3",
            "-m",
            module_name,
            "--url",
            room.url,
            "--token",
            await self.get_token(room),
            "--prompt_id",
            str(prompt_id),
        ]

        logger.info(f"Starting bot: {bot_proc}")

        try:
            subprocess.Popen(
                bot_proc,
                shell=False,
                cwd=base_path,
            )

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to start subprocess: {e}"
            )
