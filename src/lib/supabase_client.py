from supabase import create_client, Client
from models import (
    CompanyData,
    BotConfiguration,
    PhoneCaller,
    PatientData,
    InboundTestLogs,
    UpdateInboundTestLogs,
    InboundTestPrompts,
)
from constants import SUPABASE_URL, SUPABASE_KEY
from typing import Dict
import postgrest.exceptions

# Supabase tables
TASKS_TABLE = "tasks"
KNOWLEDGE_BASE_TABLE = "knowledge-bases"
CURRENT_TO_PREMIERE_TABLE = "current_to_premiere_motive"
ADULT_TO_CHILD_TABLE = "adult_to_child_motive"
MAIN_MOTIVE_PER_SPECIALITY_TABLE = "specialities_main_motive"
BOT_CONFIGURATION_TABLE = "bot_parameters"
INBOUND_TESTS_LOGS_TABLE = "inbound_tests_logs"
INBOUND_TESTS_PROMPTS_TABLE = "inbound_tests_prompts"

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Supabase URL or API Key is missing in .env")


class SupabaseClient:
    def __init__(self, url: str = SUPABASE_URL, key: str = SUPABASE_KEY):
        """Initialize the Supabase client."""
        self.db: Client = create_client(url, key)

    def get_supabase(self) -> Client:
        """Returns the shared Supabase client instance."""
        return self.db

    def get_company_data_by_config_id(self, config_id: str) -> CompanyData:
        """Fetches company data from the 'knowledge-bases' table using config_id."""
        response = (
            self.db.table(KNOWLEDGE_BASE_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )
        company_data_dict = response.data[0] if response.data else None

        response_current_to_premiere = (
            self.db.table(CURRENT_TO_PREMIERE_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )
        current_to_premiere = (
            response_current_to_premiere.data
            if response_current_to_premiere.data
            else None
        )

        current_to_premiere_data = {}
        if current_to_premiere:
            for item in current_to_premiere:
                if item.get("current") and item.get("premiere"):
                    current_to_premiere_data[item["current"]] = item["premiere"]

        adult_to_child_response = (
            self.db.table(ADULT_TO_CHILD_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )

        adult_to_child = (
            adult_to_child_response.data if adult_to_child_response.data else None
        )

        adult_to_child_data = {}
        if adult_to_child:
            for item in adult_to_child:
                if item.get("adult") and item.get("children"):
                    adult_to_child_data[item["adult"]] = item["children"]

        main_motive_per_speciality_data = {}
        main_motive_per_speciality_response = (
            self.db.table(MAIN_MOTIVE_PER_SPECIALITY_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )

        main_motive_per_speciality = (
            main_motive_per_speciality_response.data
            if main_motive_per_speciality_response.data
            else {}
        )

        if main_motive_per_speciality:
            for item in main_motive_per_speciality:
                speciality = item.get("speciality_id")
                motive = item.get("motive_id")
                if speciality and motive:
                    main_motive_per_speciality_data[speciality] = motive

        bot_configuration_response = (
            self.db.table(BOT_CONFIGURATION_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )

        bot_configuration_data = (
            bot_configuration_response.data[0]
            if bot_configuration_response.data
            else None
        )

        bot_configuration = (
            BotConfiguration(**bot_configuration_data)
            if bot_configuration_data
            else BotConfiguration(config=config_id)
        )

        print(f"Fetched company data for config_id: {config_id}")

        if company_data_dict:
            company_data = CompanyData(
                **company_data_dict,
                current_to_premiere_motive=current_to_premiere_data,
                adult_to_child_motive=adult_to_child_data,
                main_motive_per_speciality=main_motive_per_speciality_data,
                bot_configuration=bot_configuration,
            )
            company_data.agenda_id = [
                doctor.agenda_id
                for doctor in company_data.inbound_config_file.calendars
            ]
            return company_data
        else:
            raise ValueError(f"Company data not found for config_id: {config_id}")

    def create_task(
        self,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        tag: str,
        message: str,
    ) -> dict:
        """Creates a new task in the 'tasks' table."""
        fullname = (
            patient_data.first_name + " " + patient_data.last_name
            if patient_data.first_name and patient_data.last_name
            else ""
        )
        response = (
            self.db.table(TASKS_TABLE)
            .insert(
                {
                    "callId": phone_caller.id,
                    "config": company_data.config,
                    "patient_data": patient_data.model_dump(),
                    "phone_number": phone_caller.phone_number,
                    "task": message,
                    "tag": tag,
                    "status": False,
                    "email": patient_data.email,
                    "full_name": fullname,
                }
            )
            .execute()
        )
        if not response.data:
            raise Exception(f"Error creating task: {response.error}")

        return response.data

    def get_etablissements(self, config_name: str) -> Dict[str, str]:
        """Fetches etablissements from the 'knowledge-bases' table."""
        response = (
            self.db.table(KNOWLEDGE_BASE_TABLE)
            .select("config, name")
            .eq("config_name", config_name)
            .execute()
        )
        etablissements = {
            item["config"]: item["name"]
            for item in response.data
            if "config" in item and "name" in item
        }
        return etablissements

    def create_inbound_test_bot_data(
        self, room_url: str, prompt_id: int
    ) -> InboundTestLogs:
        """Creates a new inbound test bot data entry in the 'inbound_test_bot' table."""
        print(
            f"Creating inbound test bot data for room_url: {room_url} with prompt_id: {prompt_id}"
        )
        try:
            response = (
                self.db.table(INBOUND_TESTS_LOGS_TABLE)
                .insert(
                    {
                        "room_url": room_url,
                        "prompt_id": prompt_id,
                    }
                )
                .execute()
            )
            print(f"Insert response: {response}")

            if hasattr(response, "data") and response.data:
                return InboundTestLogs(**response.data[0])
            else:
                err = getattr(response, "error", None)
                raise Exception(f"Error creating inbound test bot data: {err}")

        except postgrest.exceptions.APIError as e:
            # Log full error detail
            print(f"APIError caught: {e}")
            raise
        except Exception as e:
            print(f"General error caught: {e}")
            raise

    def get_inbound_test_bot_data(self, room_url: str) -> InboundTestLogs:
        """Fetches inbound test bot data from the 'inbound_test_bot' table."""
        response = (
            self.db.table(INBOUND_TESTS_LOGS_TABLE)
            .select("*")
            .eq("room_url", room_url)
            .execute()
        )
        if response.data:
            return response.data[0]
        else:
            raise ValueError(f"No inbound test bot data found for room_url: {room_url}")

    ## only update provided fields
    def update_inbound_test_bot_data(
        self,
        new_data: UpdateInboundTestLogs,
    ):
        """Updates inbound test bot data in the 'inbound_test_bot' table."""
        update_data = new_data.model_dump(exclude_unset=True)
        if not update_data:
            raise ValueError("No fields to update provided.")

        response = (
            self.db.table(INBOUND_TESTS_LOGS_TABLE)
            .update(update_data)
            .eq("room_url", new_data.room_url)
            .execute()
        )
        if response.data:
            return response.data[0]
        else:
            raise ValueError(
                f"No inbound test bot data found for call_id: {new_data.call_id}"
            )

    def get_inbound_test_prompt_by_id(self, prompt_id: int) -> InboundTestPrompts:
        """Fetches inbound test prompt data by ID from the 'inbound_test_prompts' table."""
        response = (
            self.db.table(INBOUND_TESTS_PROMPTS_TABLE)
            .select("*")
            .eq("id", prompt_id)
            .execute()
        )
        if response.data:
            return InboundTestPrompts(**response.data[0])
        else:
            raise ValueError(f"No inbound test prompt found for ID: {prompt_id}")


supabase_client = SupabaseClient()
