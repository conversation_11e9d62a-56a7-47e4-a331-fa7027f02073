import requests
import asyncio
from requests.models import Response
from datetime import datetime
from loguru import logger
from models import CallData
from constants import INTENT_TO_TAG
from models import Appointment<PERSON>orm, PatientData, PhoneCaller, CompanyData
from constants import SECRET_DOCTOLIB_TOKEN, Intents, NODE_ENV


class N8NClient:
    def __init__(self, base_url="https://n8n-self-hosted-vocca.onrender.com/webhook"):
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()
        self.session.headers.update(
            {"Authorization": f"Bearer {SECRET_DOCTOLIB_TOKEN}"}
        )
        self.session.headers.update({"Content-Type": "application/json"})

    async def create_new_booking(
        self,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        booking_info: AppointmentForm,
        company_data: CompanyData,
        booking_provider: str,
    ):
        logger.info("n8n client create new booking")
        if booking_provider == "DOCTOLIB":
            url = "7627ef3c-9e1d-47d0-bbbf-a618a327cb20"
        elif booking_provider == "EDL":
            url = "create_booking_edl"
        else:
            raise ValueError("Invalid booking provider")

        if isinstance(booking_info.agenda_id, str):
            booking_info.agenda_id = int(booking_info.agenda_id)
            logger.warning(
                f"Booking info agenda_id converted to int: {booking_info.agenda_id}"
            )
        if isinstance(booking_info.visit_motive_id, str):
            booking_info.visit_motive_id = int(booking_info.visit_motive_id)
            logger.warning(
                f"Booking info visit_motive_id converted to int: {booking_info.visit_motive_id}"
            )
        if isinstance(booking_info.agenda_id, list):
            booking_info.agenda_id = booking_info.agenda_id[0]
            logger.warning(
                f"Booking info agenda_id converted to int: {booking_info.agenda_id}- from list"
            )

        new_meeting_info = {
            "callID": phone_caller.id,
            "config": booking_info.config,
            "number": phone_caller.phone_number,
            "agenda_id": booking_info.agenda_id,
            "equipment_agenda_id": booking_info.equipment_agenda_id,
            "client": f"{patient_data.first_name} {patient_data.last_name}",
            "visit_motive_id": booking_info.visit_motive_id,
            "visit_motive_name": booking_info.visit_motive_name,
            "type": booking_info.type,
            "start_date": booking_info.start_date.strftime("%Y-%m-%dT%H:%M:%S"),
            "end_date": booking_info.end_date.strftime("%Y-%m-%dT%H:%M:%S"),
            "patient_id": patient_data.id,
            "medecin": booking_info.medecin,
            "first_name": patient_data.first_name,
            "last_name": patient_data.last_name,
            "birth_date": (
                patient_data.birthdate.strftime("%Y-%m-%d")
                if patient_data.birthdate
                else None
            ),
            "new_patient": patient_data.is_new_patient,
            "notes": booking_info.notes,
            "company_data": company_data.model_dump(mode="json"),
            "steps": booking_info.steps,
        }

        if booking_provider == "EDL":
            new_meeting_info["numero_poste"] = booking_info.modalite_id

        res = await asyncio.to_thread(self._post, url, json=new_meeting_info)

        if len(res) > 0:
            return res[0]
        raise ValueError("Failed to create booking")

    def modify_booking(
        self,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        new_appointment: AppointmentForm,
        old_appointment: AppointmentForm,
    ):
        url = "modify_booking_therasoft"

        appointment_info = {
            "phone_caller": phone_caller,
            "patient_data": patient_data,
            "new_appointment": new_appointment,
            "old_appointment": old_appointment,
        }

        data = appointment_info.model_dump()

        try:
            response = self._post(url, json=data)
            logger.warning(f"response: {response}")
            logger.debug("webhook : response status code: ", str(response))
            return response
        except Exception as e:
            logger.error(f"Failed to send data to webhook: {str(e)}")
            raise ValueError(f"Failed to send data to webhook: {str(e)}")

    def report_calls(
        self,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        appointment_info: AppointmentForm,
    ) -> dict:
        url = "f71367a7-97aa-4dd9-a799-666f4c97cd93"
        call_duration = str(datetime.now() - phone_caller.start_time)
        if not phone_caller.intent:
            phone_caller.intent = "Not mentioned"

        if phone_caller.intent == Intents.URGENCE and company_data.all_in_task:
            phone_caller.has_note = True

        new_call = CallData(
            config=company_data.config,
            call_duration=call_duration,
            phone_caller=phone_caller,
            call_type=INTENT_TO_TAG.get(phone_caller.intent, "None"),
            patient_data=patient_data,
            appointment_data=appointment_info,
        )

        data = new_call.model_dump()

        try:
            response = self._post(url, json=data)
            logger.warning(f"response: {response}")
            logger.debug("webhook : response status code: ", str(response))
            return response
        except Exception as e:
            logger.error(f"Failed to send data to webhook: {str(e)}")
            raise ValueError(f"Failed to send data to webhook: {str(e)}")

    def confirm_appointment_metadata(
        self,
        appointment: AppointmentForm,
        company_data: CompanyData,
        booking_provider: str,
    ):
        if booking_provider == "EDL":
            url = "confirm_booking_edl"
        else:
            raise ValueError("Invalid booking provider")

        data = {
            "appointment": appointment.model_dump(mode="json"),
            "company_data": company_data.model_dump(mode="json"),
            "booking_provider": booking_provider,
        }
        response = self._post(url, json=data)
        return response

    def cancel_appointment(self, data):
        url = "delete_booking_edl"
        response = self._post(url, json=data)
        return response

    def send_error_server(self, config, phone_caller: PhoneCaller = None, traceback=""):
        if NODE_ENV == "development":
            logger.error(f"Error in n8n client: {traceback}")
            return {
                "status": "error",
                "message": "Development mode, not sending to webhook",
            }

        url = "2837eaa3-8099-4a7c-a9cf-796859a7d0ff"
        error_data = {
            "call_id": phone_caller.id if phone_caller else "",
            "config": config,
            "response": str(traceback),
        }
        try:
            response = self._post(url, json=error_data)
            logger.warning(f"response: {response}")
            logger.debug("webhook : response status code: ", str(response))
            return response
        except Exception as e:
            logger.error(f"Failed to send data to webhook: {str(e)}")
            raise ValueError(f"Failed to send data to webhook: {str(e)}")

    def _get(self, endpoint, data=None, json=None):
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        response = self.session.get(url, data=data, json=json)
        return self._handle_response(response)

    def _post(self, endpoint, data=None, json=None):
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        response = self.session.post(url, data=data, json=json)
        return self._handle_response(response)

    def _handle_response(self, response: Response):
        try:
            response.raise_for_status()  # Raises an error for 4xx/5xx responses

            content_type = response.headers.get("Content-Type", "").lower()
            if content_type.startswith("application/json"):
                return response.json()
            return response.text

        except requests.exceptions.HTTPError as err:
            return {
                "error": str(err),
                "status_code": response.status_code,
                "response": response.text[:500],
            }


n8n_client = N8NClient()
