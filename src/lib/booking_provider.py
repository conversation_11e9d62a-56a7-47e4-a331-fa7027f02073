from __future__ import annotations
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Protocol

from constants import Intents
from lib.doctolib_client import DoctolibClient
from lib.logos_client import LogosClient
from lib.edl_client import EdlClient
import os
from models import (
    Appointment,
    AppointmentForm,
    CompanyData,
    BookingProviderType,
    Doctor,
    MotiveAppointment,
    PatientData,
    PhoneCaller,
    Speciality,
)


class BookingProviderFactory:
    def __init__(self) -> None:
        pass

    @staticmethod
    def get_provider(company_data: CompanyData) -> BookingProvider:
        if company_data.booking_provider == BookingProviderType.DOCTOLIB:
            return DoctolibClient(company_data)
        elif company_data.booking_provider == BookingProviderType.LOGOS:
            if not company_data.config_name:
                raise ValueError("No config name provided")
            base_url = os.getenv("LOGOS_BASE_URL", "")
            office_code = os.getenv(
                f"{company_data.config_name.upper()}_OFFICE_CODE", ""
            )
            return LogosClient(company_data, base_url, office_code)
        elif company_data.booking_provider == BookingProviderType.EDL:
            return EdlClient(company_data)
        else:
            # TODO: fill out all the other providers
            return DoctolibClient(company_data)


class BookingProvider(Protocol):
    _provider_type: BookingProviderType = BookingProviderType.DOCTOLIB
    _date_format: str

    async def get_patient_information_by_center(
        self,
        caller_phone_number: str,
        first_name: Optional[str] = "",
        last_name: Optional[str] = "",
        birthdate: Optional[str] = "",
    ) -> List[PatientData]: ...

    async def find_patient_id(
        self,
        first_name: str,
        last_name: str,
        birth_date: Optional[str] = "",
        mobile: Optional[str] = "",
    ) -> str | None: ...

    async def create_new_booking(
        self,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        booking_info: AppointmentForm,
    ) -> AppointmentForm: ...

    async def get_next_availabilities(
        self,
        motive: MotiveAppointment,
        agenda_ids: List[str],
        min_time_difference: int = 4,
        number_of_time_slots: int = 2,
        from_date: datetime = datetime.now() + timedelta(days=1),
        is_new_patient: bool = True,
    ) -> List[Appointment]: ...

    async def get_agenda_day(
        self, agenda_ids: List[int], day: datetime, motive_ids: List[int]
    ) -> Dict[str, Any]: ...

    async def cancel_appointment(
        self, appointment: AppointmentForm, intent: Intents
    ) -> Dict[str, Any] | None: ...

    async def confirm_appointment_metadata(self, appointment: AppointmentForm): ...

    async def update_appointment_with_note(
        self, appointment: AppointmentForm, note: str
    ) -> Dict[str, Any]: ...

    async def check_availabilities_global_by_id(
        self,
        visit_motive_id: str,
        agenda_ids: List[int],
        date: datetime = datetime.now() + timedelta(days=1),
    ) -> List[Appointment]: ...

    async def get_visit_motive_by_name_or_id(
        self, motive_name: str, medecin: Optional[str] = None
    ) -> MotiveAppointment | None: ...

    async def get_doctors(self) -> List[Doctor]: ...

    async def get_doctor_by_agenda_id(self, agenda_id: int) -> Doctor | None: ...

    async def get_doctors_by_speciality(self, specialty_name: str) -> List[Doctor]: ...

    async def get_speciality_by_id(self, speciality_id: int) -> Speciality | None: ...
