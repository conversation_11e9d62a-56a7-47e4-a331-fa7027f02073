import requests
from loguru import logger
from models import (
    Appointment,
    AppointmentForm,
    BookingProviderType,
    MotiveAppointment,
    CompanyData,
    PatientData,
    <PERSON><PERSON><PERSON><PERSON>,
    Doctor,
    Speciality,
)
from typing import List, Optional
from datetime import datetime, timedelta
from constants import Intents, SECRET_DOCTOLIB_URL, SECRET_DOCTOLIB_TOKEN
from lib.n8n_client import N8NClient
import utils.appointments
import asyncio
from utils.users import patient_json_to_patient_data
from zoneinfo import ZoneInfo


class DoctolibClient:
    def __init__(self, company_data: CompanyData):
        self._date_format = "%Y-%m-%d"
        self._provider_type = BookingProviderType.DOCTOLIB
        self.base_url = SECRET_DOCTOLIB_URL.rstrip("/")
        self.company_data = company_data
        self.session = requests.Session()
        self.session.headers.update(
            {"Authorization": f"Bearer {SECRET_DOCTOLIB_TOKEN}"}
        )
        self.session.headers.update({"Content-Type": "application/json"})
        self.n8n_client = N8NClient()

    async def get_patient_information_by_center(
        self,
        caller_phone_number: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        birthdate: Optional[datetime] = None,
    ) -> List[PatientData]:
        """
        Get patient information by center.
        set is_new_patient to True if the patient is new.
        """
        endpoint = "/users/search-patient"

        if birthdate:
            birthdate = birthdate.strftime("%d-%m-%Y")

        json = {
            "phone_number": caller_phone_number,
            "first_name": first_name,
            "last_name": last_name,
            "birthdate": birthdate,
            "config": self.company_data.config,
        }

        patients = await self._get(
            endpoint,
            json=json,
            version="v2",
        )

        if len(patients) == 0:
            return [PatientData(is_new_patient=True)]

        patients_data: List[PatientData] = []
        for patient in patients:
            patient_data = patient_json_to_patient_data(
                patient,
                self.company_data,
            )
            patients_data.append(patient_data)

        if not patients_data:
            return []

        return patients_data

    async def create_new_booking(
        self,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        booking_info: AppointmentForm,
    ) -> AppointmentForm:
        if booking_info.is_booking_created:
            raise Exception("Booking already created")

        if not booking_info.is_open_motive:
            raise Exception("Cannot book because motive is closed")

        new_booking = await self.n8n_client.create_new_booking(
            phone_caller, patient_data, booking_info, self.company_data, "DOCTOLIB"
        )
        booking_info.is_booking_created = True
        booking_info.id = new_booking.get("id")
        booking_info.patient_id = new_booking.get("patient").get("id")

        return booking_info

    async def cancel_appointment(
        self,
        appointment: AppointmentForm,
        intent: Intents,
        status: str = "no_show_but_ok",
    ):
        endpoint = "/appointments"
        reason = ""
        if intent == Intents.MODIFIER:
            reason = (
                "Modification de rdv: " + appointment.notes if appointment.notes else ""
            )

        elif intent == Intents.ANNULATION:
            reason = "Annulation: " + appointment.notes if appointment.notes else ""

        try:
            status = "no_show_but_ok"
            today = datetime.now().date()

            start_date = appointment.start_date

            if not start_date:
                status = "no_show"
            else:
                # Determine if start_date is naive or aware
                if start_date.tzinfo is None:
                    # Assume it's in the same timezone as the logic requires
                    start_date = start_date.replace(tzinfo=ZoneInfo("Europe/Paris"))

                now = datetime.now(ZoneInfo("Europe/Paris"))
                list_configs_with_half_day = [
                    "config81",
                    "config57",
                    "config88",
                    "config108",
                ]

                if (
                    self.company_data.config not in list_configs_with_half_day
                    and start_date.date() == today
                ):
                    status = "no_show"

                elif self.company_data.config in list_configs_with_half_day:
                    if start_date.date() == now.date():
                        if start_date.hour < 12:
                            status = "no_show"
                        elif now.hour >= 12:
                            status = "no_show"
                        else:
                            status = "no_show_but_ok"

                if self.company_data.config == "config119":
                    if start_date > now + timedelta(days=2):
                        status = "no_show_but_ok"
                    else:
                        status = "no_show"

        except Exception as e:
            logger.error(f"Error parsing appointment date: {str(e)}")
            self.n8n_client.send_error_server(
                "cancel_appointment",
                None,
                f"Error parsing appointment date: {str(e)}",
            )
            status = "no_show_but_ok"

        json = {
            "config": appointment.config,
            "id": appointment.id,
            "status": status,
            "notes": reason,
        }
        return await self._put(
            endpoint,
            json=json,
            version="v2",
        )

    async def modify_appointment(
        self,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
        new_appointment: AppointmentForm,
        old_appointment: AppointmentForm,
    ):
        if not new_appointment.is_open_motive:
            raise Exception("Cannot book because motive is closed")

        new_appointment.agenda_id = old_appointment.agenda_id
        new_appointment.visit_motive_id = old_appointment.visit_motive_id

        print(old_appointment)
        successs = await self.create_new_booking(
            phone_caller,
            patient_data,
            new_appointment,
        )
        if successs:
            await self.cancel_appointment(old_appointment, phone_caller.intent)

    async def confirm_appointment_metadata(self, appointment: AppointmentForm):
        """
        Confirms the appointment metadata in doctolib
        Args:
            appointment: AppointmentForm - The appointment to confirm
        Returns:
            dict - return the response from the API. It will be information about the appointment if it is confirmed
        """
        endpoint = "/confirm-appt"
        custom_fields = {
            "c5b5d": "Oui",  # This is a custom field that needs to be set to "Oui"
        }

        if appointment.config == "config119":
            custom_fields = {"97425": "Confirmé par Vocca"}

        data = {
            "config": appointment.config,
            "appt_id": appointment.id,
            "custom_fields": custom_fields,
        }

        return await self._put(endpoint, json=data)

    async def update_appointment_with_note(
        self, appointment: AppointmentForm, note: str
    ):
        endpoint = "/modify-appt"
        data = {"config": appointment.config, "id": appointment.id, "note": note}
        return await self._put(endpoint, json=data)

    async def check_availabilities_global_by_id(
        self,
        visit_motive_id: str,
        agenda_ids: List[int],
        date: datetime = datetime.now() + timedelta(days=1),
    ) -> List[Appointment]:
        date = date.strftime("%Y-%m-%d")
        endpoint = "/appointments/availabilities"
        if isinstance(agenda_ids, int) or isinstance(agenda_ids, str):
            agenda_ids = [str(agenda_ids)]

        agenda_ids = [str(agenda_id) for agenda_id in agenda_ids]
        json = {
            "config": self.company_data.config,
            "agenda_id": agenda_ids,
            "visit_motive_id": visit_motive_id,
            "day": date,
        }
        print(json)

        try:
            timeslots = await self._get(
                endpoint,
                json=json,
                version="v2",
            )

            return [Appointment(**timeslot) for timeslot in timeslots]

        except Exception as e:
            logger.error(f"Error in check_availabilities_global: {str(e)}")
            return None

    async def check_availabilities_multiple(
        self,
        visit_motive_id_1: str,
        visit_motive_id_2: str,
        agenda_ids_1: List[int],
        agenda_ids_2: List[int],
        date: datetime = datetime.now() + timedelta(days=1),
    ) -> List[Appointment]:
        date = date.strftime("%Y-%m-%d")
        endpoint = "/appointments/availabilities-steps"
        if isinstance(agenda_ids_1, int) or isinstance(agenda_ids_1, str):
            agenda_ids_1 = [str(agenda_ids_1)]
        if isinstance(agenda_ids_2, int) or isinstance(agenda_ids_2, str):
            agenda_ids_2 = [str(agenda_ids_2)]

        agenda_ids_1 = [str(agenda_id) for agenda_id in agenda_ids_1]
        agenda_ids_2 = [str(agenda_id) for agenda_id in agenda_ids_2]

        json = {
            "config": self.company_data.config,
            "agenda_ids_1": agenda_ids_1,
            "agenda_ids_2": agenda_ids_2,
            "visit_motive_id_1": visit_motive_id_1,
            "visit_motive_id_2": visit_motive_id_2,
            "day": date,
        }
        print(json)
        try:
            timeslots = await self._get(
                endpoint,
                json=json,
                version="v2",
            )

            return [Appointment(**timeslot) for timeslot in timeslots]
        except Exception as e:
            logger.error(f"Error in check_availabilities_multiple: {str(e)}")
            return None

    async def get_visit_motive_by_name_or_id(
        self, motive_name: str, medecin: Optional[str] = None
    ) -> MotiveAppointment | None:
        """
        Verify if the practice type is available at the center
        Args:
            practice_type: str - The practice type to verify
            medecin: str - The doctor to verify
        Returns:
            dict - return the response from the API. It will be information about the practice type if it is available at the center
        """
        if isinstance(motive_name, int):
            motive_name = str(motive_name)

        json = {
            "config": self.company_data.config,
            "practice": motive_name,
            "medecin": medecin,
        }
        print(json)
        endpoint = "/motives"
        try:
            resp = await self._post(endpoint, json=json, version="v2")
            if resp.get("status_code") and resp.get("status_code") != 200:
                return None

            motive = MotiveAppointment(
                visit_motive_name=resp.get("visit_motive_name"),
                visit_motive_id=resp.get("visit_motive_id"),
                open=resp.get("open"),
                price=resp.get("price"),
                instructions=resp.get("instructions"),
                after=resp.get("after"),
                before=resp.get("before"),
                type=resp.get("type"),
                age_minimum=resp.get("age_minimum"),
                speciality_id=resp.get("speciality_id"),
                medecin=resp.get("medecin"),
            )

            logger.debug(f"Visit motive: {motive.visit_motive_name}")
            return motive
        except Exception as e:
            if "404" in str(e):
                logger.warning(
                    f"Visit motive {motive_name} not found for medecin {medecin}"
                )
                return None

            logger.warning(f"Error in get_visit_motive_by_name: {str(e)}")
            return None

    async def get_doctors(self, patient: PatientData = None) -> List[Doctor]:
        doctors = self.company_data.inbound_config_file.calendars
        if patient:
            patient_age = utils.appointments.calculate_age(patient.birthdate)
            doctors = [
                doctor
                for doctor in doctors
                if (doctor.age_minimum is None or patient_age >= doctor.age_minimum)
                and (doctor.age_maximum is None or patient_age <= doctor.age_maximum)
            ]
        return doctors

    async def get_doctor_by_agenda_id(self, agenda_id: int):
        doctors = await self.get_doctors()
        return next(
            (doctor for doctor in doctors if doctor.agenda_id == agenda_id), None
        )

    async def get_speciality_by_id(self, speciality_id: int) -> Speciality | None:
        return next(
            (
                item
                for item in self.company_data.inbound_config_file.specialities
                if item.id == speciality_id
            ),
            None,
        )

    async def get_doctors_by_speciality(self, speciality: int) -> List[Doctor]:
        """
        Get doctors by speciality.
        Args:
            speciality: int - The speciality id to filter the doctors
        Returns:
            List[Doctor] - List of doctors with the given speciality
        """
        doctors = await self.get_doctors()
        return [doctor for doctor in doctors if speciality == doctor.speciality_id]

    async def get_next_availabilities(
        self,
        motive: MotiveAppointment,
        agenda_ids: List[str],
        min_time_difference: Optional[int] = 4,
        number_of_time_slots: Optional[int] = 2,
        from_date: datetime = datetime.now() + timedelta(days=1),
        is_new_patient: bool = True,
        steps_motives: Optional[List[MotiveAppointment]] = None,
    ) -> List[Appointment]:

        try:
            res_next_availabilities = []
            if steps_motives and len(steps_motives) > 1:
                visit_motive_id_1 = str(steps_motives[0].visit_motive_id)
                visit_motive_id_2 = str(steps_motives[1].visit_motive_id)
                agenda_ids_1 = agenda_ids
                agenda_ids_2 = agenda_ids
                res_next_availabilities = await self.check_availabilities_multiple(
                    visit_motive_id_1,
                    visit_motive_id_2,
                    agenda_ids_1,
                    agenda_ids_2,
                    from_date,
                )
                next_availabilities = res_next_availabilities

            else:
                res_next_availabilities = await self.check_availabilities_global_by_id(
                    str(motive.visit_motive_id),
                    agenda_ids,
                    from_date,
                )
                next_availabilities = res_next_availabilities

            if not next_availabilities:
                return []

            for appointment in next_availabilities:
                appointment.visit_motive_id = motive.visit_motive_id

            if min_time_difference is None or number_of_time_slots is None:
                return next_availabilities

            suggested_timeslots = utils.appointments.get_different_time_slots(
                next_availabilities, min_time_difference, number_of_time_slots
            )

            suggested_timeslots = sorted(
                suggested_timeslots, key=lambda x: x.start_date
            )
            return suggested_timeslots
        except Exception as e:
            logger.error(f"Error getting next availabilities: {e}")
            return []

    async def get_agenda_day(
        self, agenda_ids: List[int], day: datetime, motive_ids: List[int]
    ):
        endpoint = "/agendas/day"
        agenda_ids = [str(agenda_id) for agenda_id in agenda_ids]
        motive_ids = [str(motive_id) for motive_id in motive_ids]
        day = day.strftime("%Y-%m-%d")
        json = {
            "config": self.company_data.config,
            "agenda_ids": agenda_ids,
            "day": day,
            "motive_ids": motive_ids,
        }
        return await self._get(
            endpoint,
            json=json,
            version="v2",
        )

    async def get_appointment_info(self, appointment_id: str):
        """
        Get appointment information by appointment ID.
        Args:
            appointment_id: str - The appointment ID to get information for
        Returns:
            AppointmentForm - The appointment information
        """
        endpoint = "/appointments"
        json = {
            "config": self.company_data.config,
            "id": appointment_id,
        }
        response = await self._get(endpoint, json=json, version="v2")
        if not response:
            return None

        return response

    async def _get(self, endpoint, params=None, json=None, version="v1"):
        url = f"{self.base_url.replace('v1', version)}/{endpoint.lstrip('/')}"
        response = await asyncio.to_thread(
            self.session.get, url, params=params, json=json
        )
        return self._handle_response(response)

    async def _post(self, endpoint, data=None, json=None, version="v1"):
        url = f"{self.base_url.replace('v1', version)}/{endpoint.lstrip('/')}"
        response = await asyncio.to_thread(self.session.post, url, data=data, json=json)
        return self._handle_response(response)

    async def _delete(self, endpoint):
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        response = await asyncio.to_thread(self.session.delete, url)
        return self._handle_response(response)

    async def _put(self, endpoint, data=None, json=None, version="v1"):
        url = f"{self.base_url.replace('v1', version)}/{endpoint.lstrip('/')}"
        response = await asyncio.to_thread(self.session.put, url, data=data, json=json)
        return self._handle_response(response)

    async def find_patient_id(
        self,
        first_name: str,
        last_name: str,
        birth_date: Optional[str] = "",
        mobile: Optional[str] = "",
    ) -> str | None:
        raise NotImplementedError("Not implemented for Doctolib.")

    def _handle_response(self, response):
        try:
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as err:
            logger.error(f"Request body: {response.request.body}")
            logger.error(f"Error in DoctolibClient: {err}")
            raise err
