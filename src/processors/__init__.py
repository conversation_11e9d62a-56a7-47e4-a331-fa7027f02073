from typing import Literal
import random
import sys
from loguru import logger
from pipecat.audio.vad.vad_analyzer import VADParams
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transports.services.daily import (
    DailyParams,
    DailyTransport,
)
from openai import NotGiven
from pipecat.services.tts_service import TTSService
from pipecat.services.azure.llm import AzureLLMService
from pipecat.services.azure.stt import AzureSTTService
from pipecat.services.azure.tts import AzureTTSService
from pipecat.services.openai.llm import OpenAILLMService, OpenAILLMContext
from pipecat.services.azure.common import Language
from pipecat.processors.user_idle_processor import UserIdleProcessor
from pipecat.frames.frames import TTSSpeakFrame
from pipecat.services.gladia.config import GladiaInputParams, LanguageConfig
from pipecat.services.gladia.stt import GladiaSTTService
from .soundfile_mixer import SoundfileMixer
import constants
from audios import sound_files
from models import CompanyData
from .stt_mute_filter import (
    STTMuteConfig,
    ST<PERSON>uteFilter,
    STTMuteStrategy,
)
from nodes.base.intake_processor import BaseIntakeProcessor
from .soundProcessor import DingProcessor, DingConfig, DingStrategy
from pipecat.serializers.twilio import TwilioFrameSerializer
from pipecat.transports.network.fastapi_websocket import (
    FastAPIWebsocketParams,
    FastAPIWebsocketTransport,
)


def init_daily_transport(
    mode: Literal["daily", "twilio"],
    company_data: CompanyData,
    room_url: str = None,
    token: str = None,
    websocket=None,
    stream_sid=None,
):
    soundfile_mixer = SoundfileMixer(
        {"new_office": sound_files["new_office.mp3"]},
        volume=0.2,
        default_sound="new_office",
    )

    if mode == "daily":
        # keywords = [
        #     {"keyword": "oui", "boost": 5},
        #     {"keyword": "non", "boost": 5},
        #     {"keyword": "we", "boost": -5},
        #     {"keyword": "great", "boost": -5},
        # ]

        # # Serialize keywords for the query string
        # keywords_query = "&".join(
        #     [f"keywords={k['keyword']}:{k['boost']}" for k in keywords]
        # )
        return DailyTransport(
            room_url,
            token,
            f"Chatbot {company_data.name} {company_data.config}",
            DailyParams(
                api_key=constants.DAILY_API_KEY,
                audio_out_enabled=True,
                audio_in_enabled=True,
                vad_enabled=True,
                vad_audio_passthrough=True,
                vad_analyzer=SileroVADAnalyzer(
                    params=VADParams(
                        confidence=0.2,
                        start_secs=0.01,  # Default is 0.2 seconds
                        stop_secs=1,  # Default is 0.8 seconds
                        min_volume=0.2,
                    )
                ),
                audio_out_mixer=soundfile_mixer,
                # transcription_settings=DailyTranscriptionSettings(
                #     language="multi",
                #     model="nova-3",
                #     profanity_filter=False,
                #     smart_format=False,
                #     redact=False,
                #     endpointing=True,
                #     punctuate=False,
                #     utterances=False,
                #     extra={"interim_results": False, "keywords_query": keywords_query},
                # ),
                transcription_enabled=False,
            ),
        )

    elif mode == "twilio":
        return FastAPIWebsocketTransport(
            websocket=websocket,
            params=FastAPIWebsocketParams(
                audio_in_enabled=True,
                audio_out_enabled=True,
                add_wav_header=False,
                vad_enabled=True,
                vad_analyzer=SileroVADAnalyzer(),
                vad_audio_passthrough=True,
                serializer=TwilioFrameSerializer(stream_sid),
            ),
        )
    else:
        raise ValueError(
            f"Unknown transport mode: {mode}. Expected 'daily' or 'twilio'."
        )


def init_stt_service(service_name: Literal["azure"]):
    if service_name == "azure":
        return AzureSTTService(
            api_key=random.choice(constants.AZURE_SPEECH_API_KEYS),
            region=constants.AZURE_REGION,
            language="fr-FR",
        )

    if service_name == "gladia":
        return GladiaSTTService(
            api_key=constants.GLADIA_API_KEY,
            params=GladiaInputParams(
                language_config=LanguageConfig(
                    languages=[Language.FR],
                )
            ),
        )

    if service_name == "deepgram":
        logger.warning("Already using Deepgram in Daily transport")
        return None

    raise ValueError(f"Unknown STT service: {service_name}")


def init_llm_service(
    service_name: Literal["openai"], parallel_tool_calls: bool | NotGiven = False
):
    if service_name == "openai":
        return OpenAILLMService(
            api_key=constants.OPENAI_API_KEY,
            model="gpt-4o-mini",
            params=OpenAILLMService.InputParams(
                temperature=0, extra={"parallel_tool_calls": parallel_tool_calls}
            ),
        )

    if service_name == "azure":
        return AzureLLMService(
            api_key=constants.AZURE_LLM_KEY,
            endpoint=constants.AZURE_LLM_ENDPOINT,
            model="gpt-4o",
        )

    raise ValueError(f"Unknown LLM service: {service_name}")


def init_tts_service(
    service_name: Literal["azure", "cartesia"],
    text_filter=None,
    rate="1.15",
    voice="fr-FR-DeniseNeural",
):
    if service_name == "azure":
        return AzureTTSService(
            api_key=random.choice(constants.AZURE_SPEECH_API_KEYS),
            region=constants.AZURE_REGION,
            voice=voice,
            params=AzureTTSService.InputParams(
                language=Language.FR,
                rate=rate,
                text_filter=text_filter,
            ),
        )

    raise ValueError(f"Unknown TTS service: {service_name}")


def init_user_idle(
    intake: BaseIntakeProcessor,
    context: OpenAILLMContext,
    stt: TTSService,
    timeout=20.0,
):
    async def user_idle_callback(user_idle: UserIdleProcessor):
        try:

            # Only trigger if we've received at least one user message
            if len([m for m in context.messages if m["role"] == "user"]) > 0:
                logger.warning("IDLE: Asking if anyone is there")
                phrases = [
                    " Je suis là si vous avez besoin. On continue ensemble quand vous êtes prêt. ",
                    " Pas de souci si vous prenez un moment, je reste avec vous. Dites-moi quand vous êtes prêt. ",
                    " Vous êtes toujours là ? Je suis là si jamais vous avez besoin d'un petit coup de main. ",
                ]
                msg = random.choice(phrases)
                await stt.push_frame(TTSSpeakFrame(msg))
                intake.retries_take_appointment += 1

        except Exception as e:
            logger.error(f"Error in user_idle_callback: {str(e)}")

    return UserIdleProcessor(callback=user_idle_callback, timeout=timeout)


def init_mute_filter_service():
    return STTMuteFilter(
        config=STTMuteConfig(
            strategies={STTMuteStrategy.ALWAYS},
        )
    )


def init_ding_processor(mute_processor: STTMuteFilter):
    return DingProcessor(
        config=DingConfig(
            strategies={DingStrategy.START_AFTER_BOT_SPEECH},
        ),
        mute_processor=mute_processor,
    )
